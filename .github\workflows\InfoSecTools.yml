name: InfoSec Scans


on:
  workflow_dispatch:
  push:
    branches: [ "uat" ]

permissions:
      id-token: write
      contents: read

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4

    - name: Checkmarx One CLI Action 
      uses: checkmarx/ast-github-action@2.0.23 #Github Action version 
      with: 
        project_name: Independence Document Processing
        cx_tenant: vmey 
        base_uri: https://eu.ast.checkmarx.net/ 
        cx_client_id: ${{ secrets.CX_CLIENT_ID }} 
        cx_client_secret: ${{ secrets.CX_CLIENT_SECRET }} 
        additional_params: --application-name “Independence Document Processing”

    - name: Download WhiteSource Unified Agent
      run: |
          curl -LJO https://github.com/whitesource/unified-agent-distribution/releases/latest/download/wss-unified-agent.jar  

    - name: Run Mend scan
      run: |
          java -jar wss-unified-agent.jar -c wss-unified-agent.config -apikey ${{ secrets.WS_APIKEY }} -userkey ${{ secrets.WS_USERKEY }}