name: Build and Deploy Fabric Insights Empire Api UAT

on:
  workflow_dispatch:
  push:
    branches: [uat]

permissions:
      id-token: write
      contents: read

jobs:
  build-and-deploy:
    runs-on:
      group: eyorg_linux_custom_20_4_16
    
    environment: |-
      ${{
        github.ref_name == 'develop' && 'develop'
      || github.ref_name == 'uat'    && 'uat'
      || github.ref_name == 'staging'    && 'staging'
      || github.ref_name == 'master'    && 'prod'
      || 'uat'
      }}

    steps:
    - uses: actions/checkout@v4
    
    - uses: azure/login@v2
      with:
        creds: '{"clientId": "${{secrets.AZURE_CLIENT_ID}}","clientSecret": "${{secrets.AZURE_CLIENT_SECRET}}","subscriptionId": "${{vars.AZURE_SUBSCRIPTION_ID}}","tenantId": "${{secrets.AZURE_TENANT_ID}}"}'

    - name: Set up Python 3.12
      uses: actions/setup-python@v4
      with:
        python-version: 3.12

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt

    - name: Deploy web App using GH Action azure/webapps-deploy
      uses: azure/webapps-deploy@v3
      with:
        app-name: ${{ vars.AZURE_WEBAPP_NAME }}

    - uses: azure/appservice-settings@v1
      with:
        app-name: ${{ vars.AZURE_WEBAPP_NAME }}
        mask-inputs: false
        app-settings-json: '[
                              {
                              "name": "AZURE_CLIENT_ID",
                              "value": "${{secrets.AZURE_CLIENT_ID}}", 
                              "slotSetting": false
                              },
                              {
                              "name": "AZURE_CLIENT_SECRET",
                              "value": "${{secrets.AZURE_CLIENT_SECRET}}", 
                              "slotSetting": false
                              },
                              {
                              "name": "AZURE_TENANT_ID",
                              "value": "${{secrets.AZURE_TENANT_ID}}", 
                              "slotSetting": false
                              },
                              {
                              "name": "AZURE_KEY_VAULT_URL",
                              "value": "${{vars.KEY_VAULT_URL}}", 
                              "slotSetting": false
                              }
                            ]'
      id: settings
    - name: logout
      run: |
        az logout