import sys
from pathlib import Path
#sys.path.append(str(Path(__file__).resolve().parent.parent))

import os
import logging
from openai import AzureOpenAI
from azure.core.exceptions import ResourceNotFoundError
from config.settings import config, client_openai
import asyncio

async def test_openai_deployment(prompt: str = "What is the capital of France?"):
    """
    Test the Azure OpenAI deployment by sending a chat completion request.
    """
    try:

        print(config.openai_endpoint)
        client = client_openai

        # Assert that the deployment exists
        # Test chat completion
        messages = [
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": prompt},
        ]

        response = await client.chat.completions.create(
            model="gpt-4o",
            messages=messages,
            temperature=0
        )

        # print("Chat completion response:")
        # print(response.choices[0].message.content)
        return response.choices[0].message.content

    except Exception as e:
        logging.error("Failed to test Azure OpenAI deployment.")
        logging.error(f"Error: {e}")


async def multi_exec(executions: int = 5):

    coroutines = []

    for i in range(executions):
        coroutines.append(test_openai_deployment())

    return await asyncio.gather(*coroutines)

if __name__ == "__main__":

    results = asyncio.run(multi_exec())

    for res in results:
        print(res)
 