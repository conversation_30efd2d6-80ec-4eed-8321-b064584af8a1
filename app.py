import streamlit as st
import pandas as pd
import requests
import os
import json
import fitz
from datetime import datetime
import asyncio
import time
import urllib
import urllib.parse

from auth import login_ui, logout, get_token_from_cache

from src.utils import bbox_viewer
from src.metrics import evaluation
import llm_process_runner
from src.utils import file_handler
from src.data_processing import ground_truth_processor

from src.utils.logger import get_logger
LOGGER = get_logger("app")

def main_app():
    """
    This function contains the main logic of your Streamlit application.
    It will only be called if the user is authenticated.
    """
    st.set_page_config(layout="wide")

    with st.sidebar:
        st.title("Document Extraction")
        user_info = st.session_state.get("user_info", {})
        st.write(f"Welcome, {user_info.get('name', 'User')}!")
        st.write(f"Email: {user_info.get('preferred_username', 'N/A')}")
        if st.button("Logout"):
            logout()

    st.title("Document Extraction Testing Application")

    # Initialize session_state variables to hold data across reruns and tabs.
    if 'ground_truth_loaded' not in st.session_state:
        st.session_state.ground_truth_loaded = False
    if 'df_general' not in st.session_state:
        st.session_state.df_general = pd.DataFrame()
    if 'df_holdings_transactions' not in st.session_state:
        st.session_state.df_holdings_transactions = pd.DataFrame()
    if 'last_run_results' not in st.session_state:
        st.session_state.last_run_results = {}
    if 'last_run_errors' not in st.session_state:
        st.session_state.last_run_errors = []
    if 'append_to_golden_set' not in st.session_state:
        st.session_state.append_to_golden_set = False
    if 'last_run_metrics_df' not in st.session_state:
        st.session_state.last_run_metrics_df = pd.DataFrame()
    if 'last_run_aggregated_metrics' not in st.session_state:
        st.session_state.last_run_aggregated_metrics = pd.DataFrame()
    if 'last_run_summary_metrics' not in st.session_state:
        st.session_state.last_run_summary_metrics = pd.DataFrame()
    if 'temp_artifacts' not in st.session_state:
        st.session_state.temp_artifacts = {}
    if 'runs' not in st.session_state:
        st.session_state.runs = file_handler.load_run_metadata_from_disk()
    if 'last_source_option' not in st.session_state:
        st.session_state.last_source_option = 'Golden Set'

    tab1, tab2, tab3 = st.tabs(["Testing Documents", "Run Metrics", "View Extracted Documents"])


    with tab1:
        st.header("Select Data Source")

        source_option = st.radio(
            "Choose the data source for testing:",
            ('Golden Set', 'Ad hoc Datasource'),
            horizontal=True,
            key='datasource_option'
        )

        # Detect if the user has changed the data source.
        # If so, reset the state to prevent data "leaking" between options.
        if source_option != st.session_state.last_source_option:
            st.session_state.df_general = pd.DataFrame()
            st.session_state.df_holdings_transactions = pd.DataFrame()
            st.session_state.ground_truth_loaded = False
            st.session_state.last_source_option = source_option
            st.info("Data source changed. Please load new data if required.")
            st.rerun()

        if source_option == 'Golden Set':
            # Only load data if the dataframe is empty to avoid unnecessary reloads.
            if st.session_state.df_general.empty:
                with st.spinner("Loading documents from Golden Set..."):
                    golden_set_df = file_handler.load_golden_set_documents()
                    if not golden_set_df.empty:
                        st.session_state.df_general = golden_set_df
                        st.session_state.ground_truth_loaded = True
                    else:
                        st.warning("Golden Set is empty. Add documents by running a process with 'Append to Golden set?' checked.")
                        st.session_state.ground_truth_loaded = False
            
            if st.session_state.ground_truth_loaded:
                st.info(f"Loaded {len(st.session_state.df_general)} documents from the Golden Set.")


        elif source_option == 'Ad hoc Datasource':
            uploaded_file = st.file_uploader("Upload Excel with Ground Truth", type=['xlsx', 'xls'])
            
            st.session_state.append_to_golden_set = st.checkbox("Append to Golden set?", value=False, help="If checked, the raw document, ground truth, and LLM extraction will be saved to the Golden Set folder after the run.")

            if uploaded_file is not None:
                if st.button("Extract ground truth from excel file", type="primary"):
                    # 1. Define an async function to manage the process
                    async def run_async_processing():
                        with st.spinner("Processing Excel file to extract the ground truth... This may take a moment."):
                            # 2. Call the new async version of the processor
                            df_gen, df_ht = await ground_truth_processor.async_process_ground_truth_excel(uploaded_file)
                            
                            # 3. Update session state with the results
                            st.session_state.df_general = df_gen
                            st.session_state.df_holdings_transactions = df_ht
                            st.session_state.ground_truth_loaded = True
                            st.success("Ground truth extracted and loaded into memory for this session.")

                    # 4. Run the async function from the synchronous Streamlit context
                    asyncio.run(run_async_processing())
                    
                    # 5. Rerun the app to reflect the changes in the UI
                    st.rerun()

        st.markdown("---")

        # Display the list of documents that are ready to be processed.
        if st.session_state.get('ground_truth_loaded', False):
            st.subheader("Documents to Process")
            df_display = st.session_state.df_general[['file_name', 'file_url']].drop_duplicates().reset_index(drop=True)
            st.dataframe(df_display)


    with tab2:
        if not st.session_state.get('ground_truth_loaded', False):
            st.warning("Please load a data source in the 'Testing Documents' tab first.")
        else:
            st.header("Configure and Run Extraction Process")

            st.info(f"Using documents from: **{st.session_state.datasource_option}**")
            
            col1, col2, col3 = st.columns(3)
            with col1:
                extraction_model = st.selectbox("Extraction Model", ('image_llm', 'markdown_llm'))
            with col2:
                llm_model = st.selectbox("LLM Model", ('gpt-4.1', 'gpt-4.1-mini', 'gpt-5', 'gpt-5-mini'))
                # Normalize the LLM model name to match deployments' names
                llm_model = urllib.parse.unquote(llm_model)
                llm_model = llm_model.lower().replace(" ", "-")
            with col3:
                fuzzy_threshold = st.slider("Fuzzy Similarity Threshold (%)", 0, 100, 90, 1)

            if st.button("Run Model", type="primary"):

                should_save_to_blob = (st.session_state.datasource_option == 'Golden Set') or \
                                    (st.session_state.datasource_option == 'Ad hoc Datasource' and st.session_state.get('append_to_golden_set', False))

                run_name = f"{datetime.now().strftime('%y%m%d_%H%M')}_{extraction_model}_{llm_model}"
                run_status = "FINISHED"
                start_time = time.time()
                results, errors = {}, []
                processing_times = {}
                total_pages = 0
                total_size_mb = 0
                docs_to_process = st.session_state.df_general[['file_name', 'file_url']].drop_duplicates()
                my_bar = st.progress(0, text="Initializing...")

                try:
                    with st.spinner("Calculating total size of documents..."):
                        total_size_bytes = 0
                        for url in docs_to_process["file_url"]:
                            try:
                                response = requests.head(url, allow_redirects=True, timeout=10)
                                response.raise_for_status()
                                size = int(response.headers.get('Content-Length', 0))
                                total_size_bytes += size
                            except requests.exceptions.RequestException as e:
                                st.warning(f"Could not determine size for one of the files. It will be excluded from the total. Error: {e}")
                        total_size_mb = total_size_bytes / (1024 * 1024)

                    def update_progress(value, text):
                        my_bar.progress(value, text)

                    ## PROCESS IN PARALLEL
                    results, errors, processing_times, total_tokens_from_run, page_counts = asyncio.run(
                        llm_process_runner.async_run_extraction_pipeline(
                            documents_df=docs_to_process,
                            flow_name=extraction_model,
                            llm_model_name=llm_model,
                            progress_callback=lambda p: update_progress(p, f"{int(p * 100)}% Completed ...")
                        )
                    )

                    if page_counts:
                        total_pages = sum(page_counts.values())

                    st.session_state.last_run_total_tokens = total_tokens_from_run
                    st.session_state.last_run_results = results
                    st.session_state.last_run_errors = errors
                    my_bar.progress(1.0, text="Pipeline finished!")

                    if not results:
                        run_status = "ERROR"
                        st.error("The extraction process failed for all documents. The run will be marked as an error.")

                    if results:
                        with st.spinner("Calculating performance metrics..."):
                            gt_data_dict = {}
                            if st.session_state.datasource_option == 'Ad hoc Datasource':
                                gt_data_dict = ground_truth_processor.transform_gt_to_llm_format(
                                    st.session_state.df_general,
                                    st.session_state.df_holdings_transactions
                                )
                            else:
                                gt_data_dict = file_handler.load_all_ground_truth_json()

                            if not gt_data_dict:
                                st.error("Could not load any Ground Truth data. Metrics calculation failed.")
                                run_status = "ERROR"
                            else:
                                total_metrics = {}
                                all_files_metrics_dfs = []

                                for file_name, llm_data in results.items():
                                    if file_name not in gt_data_dict:
                                        continue
                                    
                                    gt_data = gt_data_dict[file_name]
                                    file_metrics = evaluation.evaluate_fields(gt_data, llm_data, fuzzy_threshold)

                                    if file_metrics:
                                        df_file = pd.DataFrame.from_dict(file_metrics, orient='index')
                                        df_file_final = evaluation.calculate_final_metrics(df_file, include_tn_in_accuracy=False)
                                        df_file_final['file'] = file_name
                                        all_files_metrics_dfs.append(df_file_final)
                                        
                                        for key, values in file_metrics.items():
                                            if key not in total_metrics:
                                                total_metrics[key] = {'TP_Fuzzy': 0, 'TN_Fuzzy': 0, 'FP_Fuzzy': 0, 'FN_Fuzzy': 0, 'TP_Exact': 0, 'TN_Exact': 0, 'FP_Exact': 0, 'FN_Exact': 0}
                                            for metric_type in values:
                                                total_metrics[key][metric_type] += values[metric_type]

                                if all_files_metrics_dfs:
                                    df_per_file = pd.concat(all_files_metrics_dfs).reset_index().rename(columns={'index': 'field'})

                                    if processing_times:
                                        df_per_file['processing_time_seconds'] = df_per_file['file'].map(processing_times).round(2)
                                    
                                    if page_counts:
                                        df_per_file['num_pages'] = df_per_file['file'].map(page_counts)

                                    st.session_state.last_run_metrics_df = df_per_file

                                    columns_to_save = [
                                        'file', 'field','num_pages', 'TP_Fuzzy', 'TN_Fuzzy', 'FP_Fuzzy', 'FN_Fuzzy', 
                                        'TP_Exact', 'TN_Exact', 'FP_Exact', 'FN_Exact', 'processing_time_seconds'
                                    ]

                                    if 'processing_time_seconds' not in df_per_file.columns:
                                        columns_to_save.remove('processing_time_seconds')

                                    if 'num_pages' not in df_per_file.columns:
                                        columns_to_save.remove('num_pages')

                                    df_to_save = df_per_file[columns_to_save].copy()
                                    df_to_save['run_name'] = run_name
                                    df_to_save['model_used'] = llm_model
                                    df_to_save['fuzzy_threshold_at_calculation'] = fuzzy_threshold
                                    file_handler.save_metrics_results(df_to_save, run_name)

                                if total_metrics:
                                    df_agg = pd.DataFrame.from_dict(total_metrics, orient='index')
                                    df_final_aggregated = evaluation.calculate_final_metrics(df_agg, include_tn_in_accuracy=False)
                                    st.session_state.last_run_aggregated_metrics = df_final_aggregated
                                    totals_series = df_agg.sum()
                                    summary_df_raw = totals_series.to_frame().T
                                    summary_df_calculated = evaluation.calculate_final_metrics(summary_df_raw, include_tn_in_accuracy=False)
                                    summary_data = {}
                                    for match_type in ['Exact', 'Fuzzy']:
                                        precision = summary_df_calculated[f'Precision_{match_type}'].iloc[0]
                                        recall = summary_df_calculated[f'Recall_{match_type}'].iloc[0]
                                        accuracy = summary_df_calculated[f'Accuracy_{match_type}'].iloc[0]
                                        row_name = f'{match_type} Match' if match_type == 'Exact' else f'{match_type} Match (Threshold: {fuzzy_threshold}%)'
                                        summary_data[row_name] = {"Precision": precision, "Recall": recall, "Accuracy": accuracy}
                                    st.session_state.last_run_summary_metrics = pd.DataFrame.from_dict(summary_data, orient='index')
                                
                                if run_status != "ERROR":
                                    pass
                                    #st.success("Metrics calculation and aggregation complete.")
                    else:
                        st.warning("Skipping metrics calculation as there were no successful extractions.")
                        st.session_state.last_run_metrics_df = pd.DataFrame()
                        st.session_state.last_run_aggregated_metrics = pd.DataFrame()
                        st.session_state.last_run_summary_metrics = pd.DataFrame()

                    if should_save_to_blob:
                        with st.spinner("Saving LLM extraction results to Golden Set..."):
                            for file_name, llm_result in results.items():
                                file_handler.save_llm_extraction(file_name, llm_result, run_name)
                        if st.session_state.datasource_option == 'Ad hoc Datasource':
                            with st.spinner("Saving Ground Truth and Raw Documents to Golden Set..."):
                                gt_data_dict = ground_truth_processor.transform_gt_to_llm_format(
                                    st.session_state.df_general,
                                    st.session_state.df_holdings_transactions
                                )
                                for file_name in results.keys():
                                    if file_name in gt_data_dict:
                                        file_handler.save_ground_truth(file_name, gt_data_dict[file_name])
                                    doc_row = docs_to_process[docs_to_process['file_name'] == file_name]
                                    if not doc_row.empty:
                                        file_url = doc_row.iloc[0]['file_url']
                                        file_handler.save_raw_document(file_name, file_url)
                                st.success("Ground Truth and Raw Documents saved.")
                    else:
                        st.session_state.temp_artifacts[run_name] = {}
                        with st.spinner("Saving temporary artifacts to session memory..."):
                            for file_name, llm_result in results.items():
                                try:
                                    doc_row = docs_to_process[docs_to_process['file_name'] == file_name]
                                    if not doc_row.empty:
                                        file_url = doc_row.iloc[0]['file_url']
                                        response = requests.get(file_url)
                                        response.raise_for_status()
                                        raw_doc_content = response.content
                                        st.session_state.temp_artifacts[run_name][file_name] = {
                                            "raw_doc_content": raw_doc_content,
                                            "llm_extraction": llm_result,
                                            "ground_truth": gt_data_dict.get(file_name)
                                        }
                                except Exception as e:
                                    st.warning(f"Could not save temporary artifact for {file_name}: {e}")

                except Exception as e:
                    run_status = "ERROR"
                    st.error(f"A critical error occurred during the run: {e}")
                    errors.append({"file_name": "CRITICAL_ERROR", "error": str(e)})

                finally:
                    end_time = time.time()
                    duration_seconds = end_time - start_time
                    minutes, seconds = divmod(duration_seconds, 60)
                    duration_str = f"{int(minutes)} m, {int(seconds)} s"

                    final_total_tokens = st.session_state.get('last_run_total_tokens', 0)

                    new_run = {
                        "ID": f"RUN-{len(st.session_state.runs) + 1:03d}",
                        "Name": run_name, 
                        "ExtractionModel": extraction_model,
                        "LLMModel": llm_model,
                        "Documents Used": f"{st.session_state.datasource_option} {len(docs_to_process)}",
                        "DateTime": datetime.now().strftime('%Y/%m/%d %H:%M'),
                        "Status": run_status,
                        "TotalPages": total_pages,
                        "TotalTokens": final_total_tokens,
                        "ProcessingTime": duration_str,
                        "TotalSizeMB": total_size_mb,
                        "AppendedToGoldenSet": should_save_to_blob,
                        "documentsProcessed": len(results) + len(errors),
                        "SuccessfulFiles": list(results.keys()),
                        "ErrorFiles": errors
                    }
                    st.session_state.runs.append(new_run)
                    file_handler.save_run_metadata(new_run)
                
                    if run_status == "FINISHED":
                        st.success(f"Run '{run_name}' completed successfully.")
                    else:
                        st.error(f"Run '{run_name}' completed with status: {run_status}.")

                    if st.session_state.datasource_option == 'Ad hoc Datasource':
                        st.session_state.df_general = pd.DataFrame()
                        st.session_state.df_holdings_transactions = pd.DataFrame()
                        st.session_state.ground_truth_loaded = False

        st.markdown("---")
        
        st.header("Previous Runs")
        if st.session_state.runs:
            runs_df = pd.DataFrame(st.session_state.runs)

            if 'DateTime' in runs_df.columns:
                runs_df = runs_df.sort_values(by="DateTime", ascending=False)
        
            if 'ErrorFiles' in runs_df.columns:
                runs_df['ErrorFiles'] = runs_df['ErrorFiles'].astype(str)
            if 'SuccessfulFiles' in runs_df.columns:
                runs_df['SuccessfulFiles'] = runs_df['SuccessfulFiles'].astype(str)
                
            st.dataframe(runs_df.set_index("ID"))


    with tab3:
        st.header("View Bounding Boxes from Past Runs")

        if not st.session_state.runs:
            st.info("No historical runs found. Please execute a run from the 'Run Metrics' tab.")
        else:
            run_names = [run['Name'] for run in st.session_state.runs]
            selected_run_name = st.selectbox("Select a Run to View", options=run_names)

            selected_run_data = next((run for run in st.session_state.runs if run['Name'] == selected_run_name), None)

            if selected_run_data:
                st.subheader("Documents in this Run")
                
                col1, col2 = st.columns([1, 2])

                with col1:
                    doc_files = selected_run_data.get('SuccessfulFiles', [])
                    
                    if not doc_files:
                        st.info("This run has no successfully processed documents to display.")
                    else:
                        for i, doc_name in enumerate(doc_files):
                            row_cols = st.columns([1, 4, 2])
                            row_cols[0].write(f"ID{i+1:03d}")
                            row_cols[1].write(doc_name)
                            if row_cols[2].button("Preview", key=f"preview_{selected_run_name}_{doc_name}"):
                                st.session_state.preview_doc_name = doc_name
                                st.session_state.preview_run_name = selected_run_name
                                # Reset page to 1 every time a new PDF is selected
                                if doc_name.lower().endswith('.pdf'):
                                    st.session_state.pdf_page = 1
                
                with col2:
                    if ('preview_doc_name' in st.session_state and 
                        st.session_state.preview_run_name == selected_run_name):
                        
                        doc_name = st.session_state.preview_doc_name
                        st.subheader(f"Preview: {doc_name}")

                        with st.spinner("Loading document preview..."):
                            raw_doc_data = None
                            llm_json_data = None
                            gt_json_data = None
                            
                            was_appended = selected_run_data.get("AppendedToGoldenSet", False)

                            if was_appended:
                                st.info("Loading artifacts from Golden Set...")
                                raw_doc_data = file_handler.get_raw_document_bytes(doc_name)
                                llm_json_data = file_handler.get_llm_extraction_json(doc_name, selected_run_name)
                                gt_json_data = file_handler.get_ground_truth_json(doc_name)
                                if not raw_doc_data or not llm_json_data:
                                    st.error(f"Could not find all required artifacts for {doc_name} in Golden Set for run {selected_run_name}.")

                            else:
                                st.info("Loading temporary artifacts from session memory...")
                                if (selected_run_name in st.session_state.temp_artifacts and
                                    doc_name in st.session_state.temp_artifacts[selected_run_name]):
                                    
                                    temp_data = st.session_state.temp_artifacts[selected_run_name][doc_name]
                                    raw_doc_data = temp_data.get("raw_doc_content")
                                    llm_json_data = temp_data.get("llm_extraction")
                                    gt_json_data = temp_data.get("ground_truth")
                                else:
                                    st.error("Temporary data for this run was not found. It might have been cleared or this is a new session.")

                        if raw_doc_data and llm_json_data:
                            # Check the extension OR the "magic bytes" of the file content.
                            is_pdf = doc_name.lower().endswith('.pdf') or raw_doc_data.startswith(b'%PDF')

                            if is_pdf:
                                try:
                                    with fitz.open(stream=raw_doc_data, filetype="pdf") as doc:
                                        num_pages = len(doc)
                                        page_number = st.number_input(
                                            "Select Page", 
                                            min_value=1, 
                                            max_value=num_pages, 
                                            value=st.session_state.get('pdf_page', 1),
                                            key=f"page_selector_{doc_name}"
                                        )
                                        st.session_state.pdf_page = page_number
                                        
                                        fig = bbox_viewer.plot_boxes_on_pdf_page(page_number, llm_json_data, doc)
                                        if fig:
                                            st.pyplot(fig, use_container_width=True)
                                except Exception as e:
                                    st.error(f"Could not process the PDF from memory: {e}")
                            else: 
                                fig = bbox_viewer.plot_boxes_on_image(llm_json_data, raw_doc_data)
                                if fig:
                                    st.pyplot(fig, use_container_width=True)
                            
                            st.markdown("---")
                            json_col1, json_col2 = st.columns(2)

                            with json_col1:
                                st.subheader("Ground Truth Data")
                                if gt_json_data:
                                    st.json(gt_json_data, expanded=False)
                                else:
                                    st.warning("Ground Truth data not found for this document.")
                            
                            with json_col2:
                                st.subheader("LLM Extraction Data")
                                st.json(llm_json_data, expanded=False)

                    else:
                        st.info("Click 'Preview' on a document to see its bounding boxes here.")


def main():
    """
    Main function to handle the application flow.
    It checks for authentication status and displays the appropriate UI.
    """
    # Initialize session state for authentication
    if "authenticated" not in st.session_state:
        st.session_state["authenticated"] = False

    # Try to get a token from cache to see if the user is already logged in
    token_response = get_token_from_cache()
    if token_response:
        st.session_state["authenticated"] = True
        if "user_info" not in st.session_state:
             st.session_state["user_info"] = token_response.get("id_token_claims")

    # Display the appropriate UI based on authentication status
    if st.session_state["authenticated"]:
        main_app()
    else:
        login_ui()

if __name__ == "__main__":
    main()