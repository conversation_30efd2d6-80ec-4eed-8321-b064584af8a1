import msal
import streamlit as st
from config.settings import config

def initialize_msal_app():
    """
    Initializes the MSAL PublicClientApplication.
    It uses Streamlit's session_state to cache the application object.
    """
    if 'msal_app' not in st.session_state:
        st.session_state.msal_app = msal.ConfidentialClientApplication(
            client_id=config.CLIENT_ID,
            authority=config.AUTHORITY,
            client_credential=config.CLIENT_SECRET
        )
    return st.session_state.msal_app

def get_token_from_cache():
    """
    Tries to silently acquire a token from the cache.
    """
    app = initialize_msal_app()
    accounts = app.get_accounts()
    if accounts:
        # Assuming the first account is the one we want to use
        result = app.acquire_token_silent(config.SCOPES, account=accounts[0])
        return result
    return None

def login_ui():
    """
    Displays the login UI and handles the authentication flow.
    """
    st.title("Welcome to the Document Extraction App")
    st.write("Please log in to continue.")

    app = initialize_msal_app()
    
    # This is the core of the auth flow.
    # 1. The user is redirected to Microsoft's login page.
    # 2. After successful login, Microsoft redirects back to our app with an authorization code.
    # 3. We use this code to acquire an access token.
    
    auth_code = st.query_params.get("code")

    if auth_code:
        # If a code is present in the URL, exchange it for a token
        try:
            token_response = app.acquire_token_by_authorization_code(
                code=auth_code,
                scopes=config.SCOPES,
                redirect_uri=config.REDIRECT_URI
            )
            
            if "access_token" in token_response:
                st.session_state["authenticated"] = True
                st.session_state["user_info"] = token_response.get("id_token_claims")
                # Clear the query params and rerun to show the main app
                st.query_params.clear()
                st.rerun()
            elif "error" in token_response:
                st.error(f"Login failed: {token_response.get('error_description', 'Unknown error')}")

        except Exception as e:
            st.error(f"An error occurred during token acquisition: {e}")

    else:
        # If no code, show the login button
        auth_url = app.get_authorization_request_url(
            scopes=config.SCOPES,
            redirect_uri=config.REDIRECT_URI
        )
        # Use markdown for a clickable link that looks like a button
        st.markdown(f'<a href="{auth_url}" target="_self" style="text-decoration: none;"><button style="padding: 10px 20px; background-color: #0078D4; color: white; border: none; border-radius: 5px; cursor: pointer;">Login with Microsoft</button></a>', unsafe_allow_html=True)

def logout():
    """
    Logs the user out by clearing the session state and MSAL cache.
    """
    app = initialize_msal_app()
    accounts = app.get_accounts()
    if accounts:
        for account in accounts:
            app.remove_account(account)
    
    # Clear all session state variables
    for key in list(st.session_state.keys()):
        del st.session_state[key]
    
    st.rerun()