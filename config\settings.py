import os
from dataclasses import dataclass, field
from dotenv import load_dotenv
from openai import AsyncAzureOpenAI, AzureOpenAI

from src.services.key_vault_handler import KeyVaultHandler

load_dotenv(override=True)

kv_handler = KeyVaultHandler(
    vault_url=os.getenv("AZURE_KEY_VAULT_URL"),
    client_id=os.getenv("AZURE_CLIENT_ID"),
    tenant_id=os.getenv("AZURE_TENANT_ID"),
    client_secret=os.getenv("AZURE_CLIENT_SECRET")
)

def load_from_kv_or_env(kv_handler: KeyVaultHandler, secret_name:str, default: str = None) -> str:

    env_var = os.getenv(secret_name)  # Ensure the environment variable is loaded
    if env_var:
        return env_var

    kv_secret = kv_handler.get_secret(secret_name)
    return kv_secret if kv_secret else default

@dataclass
class AppConfig:
    # Azure OpenAI Configuration
    openai_endpoint: str = load_from_kv_or_env(kv_handler, "OpenAIEndpoint")
    openai_api_key: str = load_from_kv_or_env(kv_handler, "OpenAIAPIKey")
    api_version: str = "2025-01-01-preview"

    # Azure Blob Storage Configuration
    blob_storage_connection_string: str = load_from_kv_or_env(kv_handler, "AzureWebJobsStorage")
    container_name: str = "metric-process"

    # DI configuration
    DI_endpoint: str = load_from_kv_or_env(kv_handler, "DIEndpointAzure")
    DI_api_key: str = load_from_kv_or_env(kv_handler, "DI-API-KEY")

    # App Registration
    CLIENT_ID = load_from_kv_or_env(kv_handler, "AppRegClientId")
    TENANT_ID = load_from_kv_or_env(kv_handler, "TenantIdAppReg")
    CLIENT_SECRET: str = load_from_kv_or_env(kv_handler, "AdminConsoleAppReg")
    AUTHORITY: str = f"https://login.microsoftonline.com/{TENANT_ID}"
    SCOPES: list = field(default_factory=lambda: ["User.Read"])
    REDIRECT_URI: str = load_from_kv_or_env(kv_handler, "RedirectURI")

    # Flow selection: "original" or "alternative"
    # llm_model: str = load_from_kv_or_env(kv_handler, "LLM_MODEL", default="img_llm")

    # Key Vault Configuration
    AZURE_KEY_VAULT_URL: str = os.getenv("AZURE_KEY_VAULT_URL")
    AZURE_TENANT_ID: str = os.getenv("AZURE_TENANT_ID")
    AZURE_CLIENT_ID: str = os.getenv("AZURE_CLIENT_ID")
    AZURE_CLIENT_SECRET: str = os.getenv("AZURE_CLIENT_SECRET")

    # Prompts directory
    PROMPTS_IMAGE_DIR: str = os.path.join(os.path.dirname(__file__), '..', 'prompts', 'image_prompts')
    PROMPTS_MARKDOWN_DIR: str = os.path.join(os.path.dirname(__file__), '..', 'prompts', 'markdown_prompts')

    # System prompt for LLM
    SYSTEM_PROMPT: str = "You are a specialized financial document analysis system, designed to extract, validate, and structure information from financial statements and reports."

    # Fields to exclude from the llm output
    EXCLUDED_FIELDS = {
        'page_number', 'confidence', 'bounding_regions', 
        #'id', 'ime_id', 'ps_id', 'imeacc_id', 'psacc_id'
    }

config = AppConfig()

# Initialize Azure OpenAI client
client_openai = AsyncAzureOpenAI(
    api_key=config.openai_api_key,
    azure_endpoint=config.openai_endpoint,
    api_version=config.api_version,
)