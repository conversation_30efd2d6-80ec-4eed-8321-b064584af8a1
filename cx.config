{"version": 1.0, "sast": {"preset": "EY-Recommended", "incremental": "false", "forceScan": "true", "fileExcludes": "*.DS_Store, *.ipr, *.iws, *.bak, *.tmp, *.aac, *.aif, *.iff, *.m3u, *.mid, *.mp3, *.mpa, *.ra, *.wav, *.wma, *.3g2, *.3gp, *.asf, *.asx, *.avi, *.flv, *.mov, *.mp4, *.mpg, *.rm, *.swf, *.vob, *.wmv, *.bmp, *.gif, *.jpg, *.png, *.psd, *.tif, *.swf, *.zip, *.rar, *.exe, *.dll, *.pdb, *.7z, *.gz, *.tar.gz, *.tar, *.ahtm, *.ahtml, *.fhtml, *.hdm, *.hdml, *.hsql, *.ht, *.hta, *.htc, *.htd, *.war, *.ear, *.htmls, *.ihtml, *.mht, *.mhtm, *.mhtml, *.ssi, *.stm, *.bin, *.lock, *.svg, *.obj, *.stml, *.ttml, *.txn, *.xhtm, *.xhtml, *.class, *.iml, *.dll, *.config, *.gradle, *.jar, *.json, *.properties, *.xml, OSADependencies.json", "folderExcludes": "_cvs, .svn, _svn, .hg, .git, .bzr, .gitgnore, .gradle, .checkstyle, .classpath, bin, obj, backup, .idea, node_modules, .github, .mvn, .nuget, .nyc_output, .python_packages, .settings, .templates, .vs, .vscode, @angular, @angular-devkit, *bootstrap*, __mock__, __mocks__, __tests__, _deploysql, _examples, _generated, _pytest, _test_, _vendor, _vendored_packages, 05.testdata, 16.5_enabler react to webapi - uploaded files, 4qcx5vp2a0pbczjic2tlvs3qj, 99.examples, build, build-cache-1, builder, *tests, dashboard-deployment*, debug, deploy, deployment*, deploy-templates, dist, dists, docs, doctemplates, e2e, entityframework.*, *test, gradle-*, *jquery*, maven*, microsoft.*, mock, mockdata, mocks, modernizr.*, mstest.*, project-cache, react, sampledata, samples, software-deploy*, swagger*, system.diagnostics*, target, testatominstance, unit-test-data, unittestprojectcosmos, unity, unity.*, net35-unity.*, vendor, vendor-details, vendorfilter, wixsharp.*, z.entityframework.*, avscan, angular, *testing*, packages*, Checkmarx/Reports "}, "additionalProperties": {"cxFlow": {"zipExclude": ".git/.*, src/test/.*, target/.*, .vs/.*, .vscode/.*"}}, "Requestor Email ID": "<EMAIL>", "Security Consultant": "<PERSON>", "SPR Number": "SPR-18902", "Application ID": "APM0023520", "Description": "Scan IDP admin console repo"}