import os
from openai import AzureOpenAI
import json
 
endpoint = "https://swcuoai4w0aoa01.openai.azure.com/"
 
subscription_key = "5d16f2c33b1f4c56b06ee70e9f4f48eb"
api_version = "2024-12-01-preview"
 
client = AzureOpenAI(
    api_version=api_version,
    azure_endpoint=endpoint,
    api_key=subscription_key,
)
 
response = client.chat.completions.create(
    messages=[
        {
            "role": "system",
            "content": "You are a helpful assistant.",
        },
        {
            "role": "user",
            "content": "I am going to Rome, what should I see?, output in a Json format",
        }
    ],
    model="gpt-5",
    response_format={"type": "json_object"}
)

usage = response.usage if hasattr(response, 'usage') else None

# Accede a los atributos del objeto 'usage'
prompt_tokens = usage.prompt_tokens if usage else 0
completion_tokens = usage.completion_tokens if usage else 0

tokens = prompt_tokens + completion_tokens

#print(response)
 
#print(response.choices[0].message.content)

print(f"Prompt tokens: {prompt_tokens}, Completion tokens: {completion_tokens}, Total tokens: {tokens}")

