import pandas as pd
import asyncio
import time
from typing import Callable, Dict, Any
from azure.core.exceptions import HttpResponseError
from config.settings import config, client_openai
from src.services.llm_handler import LLMHandler
from src.flows.image_flow import ImageFlow
from src.flows.markdown_flow import MarkdownFlow
from src.utils.format_bounding_boxes import FormatBoundingBoxes
from azure.core.credentials import AzureKeyCredential
from azure.ai.documentintelligence import DocumentIntelligenceClient
from azure.ai.documentintelligence.models import AnalyzeDocumentRequest
from azure.ai.documentintelligence.aio import DocumentIntelligenceClient as AsyncDocumentIntelligenceClient
from src.flows.base_flow import BaseFlow

from src.utils.logger import get_logger
LOGGER = get_logger("llm_process_runner")

def get_flow(flow_name: str, llm_handler: LLMHandler, document_intelligence_client: DocumentIntelligenceClient):
    """Factory to create the appropriate flow instance."""
    if flow_name == "image_llm":
        return ImageFlow(llm_handler)
    elif flow_name == "markdown_llm":
        return MarkdownFlow(llm_handler, document_intelligence_client)
    else:
        LOGGER.warning(f"Unknown flow: {flow_name}. Defaulting to 'image_llm'.")
        return ImageFlow(llm_handler)
    
def run_extraction_pipeline(documents_df: pd.DataFrame, flow_name: str, llm_model_name: str, progress_callback=None):
    """
    Runs the document extraction pipeline for a given list of documents.

    Args:
        documents_df (pd.DataFrame): DataFrame with 'file_name' and 'file_url' columns.
        flow_name (str): The name of the flow to use (e.g., "image_llm").
        llm_model_name (str): The name of the LLM model to use (for configuration).

    Returns:
        tuple: A tuple containing (results_dict, error_files_list).
               - results_dict: A dictionary with file_name as key and the extracted JSON as value.
               - error_files_list: A list of dictionaries with error information.
    """
    LOGGER.info("Starting the LLM extraction pipeline.")
    
    endpoint = config.DI_endpoint
    api_key = config.DI_api_key
    credential = AzureKeyCredential(api_key)
    document_intelligence_client = DocumentIntelligenceClient(endpoint, credential)
    
    # Determine prompt directories
    if flow_name == "markdown_llm":
        prompts_dir = config.PROMPTS_MARKDOWN_DIR
    else: # Default to image_llm
        prompts_dir = config.PROMPTS_IMAGE_DIR

    # Initialize handlers and flows
    llm_handler = LLMHandler(
        client=client_openai,
        prompts_dir=prompts_dir,
        system_prompt=config.SYSTEM_PROMPT,
        llm_model_name=llm_model_name
    )

    flow = get_flow(flow_name, llm_handler, document_intelligence_client)
    LOGGER.info(f"Using flow: {flow_name} with LLM model: {llm_model_name}")

    successful_results = {}
    error_files = []

    total_files = len(documents_df)
    processed_count = 0
    
    for index, row in documents_df.iterrows():
        file_name = row.get('file_name')
        file_url = row.get('file_url')
        
        if not file_name or not file_url:
            LOGGER.warning(f"Skipping row {index} due to missing 'file_name' or 'file_url'.")
            continue
        
        LOGGER.info(f"Processing document: {file_name}")
        
        # 1. Analyze with Document Intelligence
        try:
            poller = document_intelligence_client.begin_analyze_document(
                "prebuilt-layout", AnalyzeDocumentRequest(url_source=file_url)
            )
            json_result = poller.result().as_dict()
        except Exception as e:
            LOGGER.error(f"Error analyzing document {file_name}: {e}")
            error_files.append({"file_name": file_name, "error": f"Document Intelligence failed: {str(e)}"})
            continue
        
        # 2. Process with the LLM flow
        result = flow.process_document(file_url, file_name, json_result)
        
        if result is None:
            error_files.append({"file_name": file_name, "error": "Processing with the flow returned no results."})
            continue

        # 3. Format Bounding Boxes and save the result
        try:
            bbox_client = FormatBoundingBoxes(json_result)
            data_with_bbox = bbox_client.run(result)
            successful_results[file_name] = data_with_bbox
            LOGGER.info(f"Successfully processed {file_name}.")
        except Exception as e:
            LOGGER.error(f"Error processing bounding boxes for {file_name}: {e}")
            error_files.append({"file_name": file_name, "error": f"Bounding Box processing failed: {str(e)}"})

        processed_count += 1
        if progress_callback:
            progress_callback(processed_count / total_files)
    LOGGER.info("Extraction Pipeline Finished.")
    LOGGER.info(f"Successfully processed: {len(successful_results)} files.")
    if error_files:
        LOGGER.warning(f"Failed to process: {len(error_files)} files.")
    
    return successful_results, error_files

def update_progress(callback_func: Callable, process_stage: float):
    if callback_func:
        callback_func(process_stage)


async def async_doc_intel_analysis(documents_df: pd.DataFrame):

    endpoint = config.DI_endpoint
    api_key = config.DI_api_key
    credential = AzureKeyCredential(api_key)
    results_coroutines = []
    successful_results = {}
    errors = []

    async with AsyncDocumentIntelligenceClient(endpoint, credential) as document_intelligence_client:
        # Semaphore limits the number of concurrent tasks.
        semaphore = asyncio.Semaphore(15)
        tasks = []

        async def analyze_document(file_url: str, file_name: str):
            # Acquire the semaphore before running the task.
            start_time = time.monotonic()
            async with semaphore:
                max_retries = 5
                base_delay = 2  # seconds

                for attempt in range(max_retries):
                    try:
                        poller = await document_intelligence_client.begin_analyze_document(
                            "prebuilt-layout", AnalyzeDocumentRequest(url_source=file_url)
                        )
                        poller_result = await poller.result()
                        json_result = poller_result.as_dict()
                        LOGGER.info(f"Document processed successfully: {file_name}.")

                        # Calculate the number of pages
                        pages = json_result.get("pages", [])
                        num_pages = len(pages)

                        duration = time.monotonic() - start_time

                        return {
                            "status": "success", 
                            "values": {
                                "file_url": file_url, 
                                "file_name": file_name, 
                                "result": json_result,
                                "duration": duration,
                                "num_pages": num_pages
                            }
                        }
                    except HttpResponseError as e:
                        if e.status_code == 429:
                            if attempt < max_retries - 1:
                                delay = base_delay * (2 ** attempt)
                                LOGGER.warning(f"Rate limit reached for {file_name}. Retrying in {delay} seconds...")
                                await asyncio.sleep(delay)
                            else:
                                # If the maximum number of retries is reached, log the error.
                                LOGGER.error(f"Maximum retries reached for {file_name}. Aborting analysis.")
                                error_message = f"Document Intelligence failed after multiple retries: {str(e)}"
                                return {"status": "error", "values": [{"file_name": file_name, "error": error_message}]}
                        else:
                            # If it's another HTTP error, log it and stop.
                            LOGGER.error(f"HTTP error analyzing document {file_name}: {e}")
                            error_message = f"Document Intelligence failed with an HTTP error: {str(e)}"
                            return {"status": "error", "values": [{"file_name": file_name, "error": error_message}]}
                    except Exception as e:
                        # Capture any other unexpected exceptions.
                        LOGGER.error(f"Unexpected error analyzing document {file_name}: {e}")
                        error_message = f"Document Intelligence failed: {str(e)}"
                        return {"status": "error", "values": [{"file_name": file_name, "error": error_message}]}

        # Create a task for each document in the DataFrame
        for _, document in documents_df.iterrows():
            file_name = document.get('file_name')
            file_url = document.get('file_url')

            if not file_name or not file_url:
                LOGGER.warning(f"Skipping document due to missing 'file_name' or 'file_url'.")
                continue

            LOGGER.info(f"Adding document to processing queue: {file_name}")
            tasks.append(analyze_document(file_url, file_name))
        # Executes all tasks in parallel (limited by the semaphore)
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Process the results of the tasks
    for result in results:
        if isinstance(result, Exception):
            # Handle uncaught exceptions during the execution of gather
            LOGGER.error(f"Task failed with an uncaught exception: {result}")
            errors.append({"file_name": "Unknown", "error": str(result)})
        elif result and result.get("status") == "error":
            errors.extend(result["values"])
        elif result and result.get("status") == "success":
            file_name = result["values"]["file_name"]
            successful_results[file_name] = result["values"]

    return {"successful_results": successful_results, "errors": errors}

async def process_with_llm_flow(flow: BaseFlow, analysis_results: dict):
    flow_results = {}
    error_files = []
    timings = {}
    total_tokens = 0

    CONCURRENT_LIMIT_OPENAI = 30
    semaphore = asyncio.Semaphore(CONCURRENT_LIMIT_OPENAI)

    async def timed_process_wrapper(file_name: str, file_url: str, json_result: dict):
        async with semaphore:
            start_time = time.monotonic()
            LOGGER.info(f"Processing document: {file_name} with flow: {flow.__class__.__name__}")
            flow_output = await flow.async_process_document(file_url, file_name, json_result)
            duration = time.monotonic() - start_time
            return flow_output, duration

    tasks: list[asyncio.Task] = []

    async with asyncio.TaskGroup() as tg:
        for file_name, analysis_result in analysis_results.items():
            file_url = analysis_result.get("file_url")
            json_result = analysis_result.get("result")

            coroutine = timed_process_wrapper(file_name, file_url, json_result)
            task = tg.create_task(coroutine)
            task.set_name(file_name)
            tasks.append(task)

    for task in tasks:
        if not task:
            continue

        # Get the file name associated with this specific task
        file_name = task.get_name()
        try:
            flow_output, duration = task.result()

            if flow_output is None or "result" not in flow_output:
                error_files.append({"file_name": file_name, "error": "Processing with the flow returned no results."})
                continue
        
            flow_results[file_name] = flow_output["result"]
            total_tokens += flow_output.get("tokens", 0)
            timings[file_name] = duration

        except Exception as e:
            # If the task failed (e.g., OpenAI API error), catch it here
            LOGGER.error(f"Error processing document {file_name} in LLM flow: {e}")
            error_files.append({"file_name": file_name, "error": f"LLM flow failed: {str(e)}"})

    return {"successful_results": flow_results, "errors": error_files, "timings": timings, "total_tokens": total_tokens}


def update_progress(callback_func: Callable, process_stage: float):
    if callback_func:
        callback_func(process_stage)


async def async_run_extraction_pipeline(documents_df: pd.DataFrame, flow_name: str, llm_model_name: str, progress_callback=None):
    """
    Runs the document extraction pipeline for a given list of documents.

    Args:
        documents_df (pd.DataFrame): DataFrame with 'file_name' and 'file_url' columns.
        flow_name (str): The name of the flow to use (e.g., "image_llm").
        llm_model_name (str): The name of the LLM model to use (for configuration).

    Returns:
        tuple: A tuple containing (results_dict, error_files_list).
               - results_dict: A dictionary with file_name as key and the extracted JSON as value.
               - error_files_list: A list of dictionaries with error information.
               - processing_times: A dictionary with processing time per file.
               - total_tokens: The total tokens used in the run.
               - page_counts: A dictionary with the number of pages per file.
    """
    LOGGER.info(f"Starting the LLM extraction pipeline. {llm_model_name}")
    
    endpoint = config.DI_endpoint
    api_key = config.DI_api_key
    credential = AzureKeyCredential(api_key)

    processing_times: Dict[str, float] = {}
    
    async with AsyncDocumentIntelligenceClient(endpoint, credential) as document_intelligence_client:
    
        # Determine prompt directories
        if flow_name == "markdown_llm":
            prompts_dir = config.PROMPTS_MARKDOWN_DIR
        else: # Default to image_llm
            prompts_dir = config.PROMPTS_IMAGE_DIR

        # Initialize handlers and flows
        llm_handler = LLMHandler(
            client=client_openai,
            prompts_dir=prompts_dir,
            system_prompt=config.SYSTEM_PROMPT,
            llm_model_name=llm_model_name
        )

        flow = get_flow(flow_name, llm_handler, document_intelligence_client)
        LOGGER.info(f"Using flow: {flow_name} with LLM model: {llm_model_name}")

        successful_results = {}
        error_files = []
        ocr_llm_result = {}

        # 1. Analyze with Document Intelligence
        doc_intel_results = await async_doc_intel_analysis(documents_df)
        error_files.extend(doc_intel_results["errors"])
        ocr_results = doc_intel_results["successful_results"]

        # Store DI processing times
        for file_name, result in ocr_results.items():
            processing_times[file_name] = result.get("duration", 0)

        update_progress(progress_callback, 0.33)
        LOGGER.info(f"Document Intelligence Analysis Finished. Returned {len(ocr_results)} docs")

        # 2. Process with the LLM flow
        flow_run_output = await process_with_llm_flow(flow, ocr_results)

        error_files.extend(flow_run_output["errors"])
        flow_results = flow_run_output["successful_results"]
        flow_timings = flow_run_output["timings"]
        total_tokens = flow_run_output["total_tokens"]

        if total_tokens > 0:
            LOGGER.info(f"Total tokens used: {total_tokens}")

        # Add LLM flow processing times to the total
        for file_name, duration in flow_timings.items():
            if file_name in processing_times:
                processing_times[file_name] += duration
            else:
                processing_times[file_name] = duration

        update_progress(progress_callback, 0.66)
        LOGGER.info(f"LLM Flow Processing Finished. Returned {len(flow_results)} docs")

        # 3. Match OCR results with LLM results
        for file_name, flow_result in flow_results.items():
            if file_name in ocr_results:
                ocr_llm_result[file_name] = {
                    "ocr": ocr_results[file_name],
                    "llm": flow_result
                }

    # 4. Format Bounding Boxes and save the result
    for file_name, result in ocr_llm_result.items():
        try:
            bbox_start_time = time.monotonic()
            bbox_client = FormatBoundingBoxes(result["ocr"]["result"])
            data_with_bbox = bbox_client.run(result["llm"])
            successful_results[file_name] = data_with_bbox
            bbox_duration = time.monotonic() - bbox_start_time

            # Add bounding box processing time
            if file_name in processing_times:
                processing_times[file_name] += bbox_duration

            LOGGER.info(f"Successfully processed {file_name}.")
        except Exception as e:
            LOGGER.error(f"Error processing bounding boxes for {file_name}: {e}")
            error_files.append({"file_name": file_name, "error": f"Bounding Box processing failed: {str(e)}"})
    
    page_counts = {file_name: result.get("num_pages", 0) for file_name, result in ocr_results.items() if file_name in successful_results}

    update_progress(progress_callback, 1.0)
    LOGGER.info("Extraction Pipeline Finished.")
    LOGGER.info(f"Successfully processed: {len(successful_results)} files.")
    if error_files:
        LOGGER.warning(f"Failed to process: {len(error_files)} files.")

    return successful_results, error_files, processing_times, total_tokens, page_counts