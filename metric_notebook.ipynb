{"cells": [{"cell_type": "code", "execution_count": 1, "id": "fe2d3bf8", "metadata": {}, "outputs": [], "source": ["import pandas as pd"]}, {"cell_type": "code", "execution_count": 2, "id": "b2ed2809", "metadata": {}, "outputs": [], "source": ["def calculate_final_metrics(df, include_tn_in_accuracy=False):\n", "    \"\"\"\n", "    Calculates final metrics from a DataFrame of raw counts.\n", "\n", "    Args:\n", "        df (pd.DataFrame): DataFrame with TP, FP, FN, TN counts.\n", "        include_tn_in_accuracy (bool): If True, use the standard accuracy formula.\n", "                                       If False, use a formula that ignores TNs to avoid inflation.\n", "    \"\"\"\n", "    # Evitar divisiones por cero reemplazando NaN con 0\n", "    df['Precision_Fuzzy'] = (df['TP_Fuzzy'] / (df['TP_Fuzzy'] + df['FP_Fuzzy'])).fillna(0)\n", "    df['Recall_Fuzzy'] = (df['TP_Fuzzy'] / (df['TP_Fuzzy'] + df['FN_Fuzzy'])).fillna(0)\n", "\n", "    df['Precision_Exact'] = (df['TP_Exact'] / (df['TP_Exact'] + df['FP_Exact'])).fillna(0)\n", "    df['Recall_Exact'] = (df['TP_Exact'] / (df['TP_Exact'] + df['FN_Exact'])).fillna(0)\n", "\n", "    if include_tn_in_accuracy:\n", "        # Standard accuracy calculation: (TP + TN) / (TP + TN + FP + FN)\n", "        total_fuzzy = df['TP_Fuzzy'] + df['TN_Fuzzy'] + df['FP_Fuzzy'] + df['FN_Fuzzy']\n", "        df['Accuracy_Fuzzy'] = ((df['TP_Fuzzy'] + df['TN_Fuzzy']) / total_fuzzy).fillna(0)\n", "\n", "        total_exact = df['TP_Exact'] + df['TN_Exact'] + df['FP_Exact'] + df['FN_Exact']\n", "        df['Accuracy_Exact'] = ((df['TP_Exact'] + df['TN_Exact']) / total_exact).fillna(0)\n", "    else:\n", "        # Accuracy calculation ignoring TNs: (TP) / (TP + FP + FN)\n", "        total_fuzzy = df['TP_Fuzzy'] + df['FP_Fuzzy'] + df['FN_Fuzzy']\n", "        df['Accuracy_Fuzzy'] = (df['TP_Fuzzy'] / total_fuzzy).fillna(0)\n", "\n", "        total_exact = df['TP_Exact'] + df['FP_Exact'] + df['FN_Exact']\n", "        df['Accuracy_Exact'] = (df['TP_Exact'] / total_exact).fillna(0)\n", "\n", "    return df"]}, {"cell_type": "code", "execution_count": 4, "id": "ef586a3a", "metadata": {}, "outputs": [], "source": ["df = pd.read_csv(\"metrics_files/metrics_250925_0801_markdown_llm_gpt-4.1.csv\")"]}, {"cell_type": "code", "execution_count": 5, "id": "b3ddadcf", "metadata": {}, "outputs": [{"data": {"text/plain": ["(1722, 14)"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["df.shape"]}, {"cell_type": "code", "execution_count": 6, "id": "06668101", "metadata": {}, "outputs": [{"data": {"text/plain": ["(966, 14)"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["df[df[\"TP_Fuzzy\"] > 0].shape"]}, {"cell_type": "code", "execution_count": 7, "id": "56b1ea42", "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['file', 'field', 'TP_Fuzzy', 'TN_Fuzzy', 'FP_Fuzzy', 'FN_Fuzzy',\n", "       'TP_Exact', 'TN_Exact', 'FP_Exact', 'FN_Exact',\n", "       'processing_time_seconds', 'run_name', 'model_used',\n", "       'fuzzy_threshold_at_calculation'],\n", "      dtype='object')"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["df.columns"]}, {"cell_type": "code", "execution_count": 8, "id": "8752f938", "metadata": {}, "outputs": [], "source": ["df_processing_time = df[[\"file\", \"processing_time_seconds\"]].drop_duplicates(\"file\").sort_values(by=\"processing_time_seconds\", ascending=False)"]}, {"cell_type": "code", "execution_count": 9, "id": "4670cd07", "metadata": {}, "outputs": [{"data": {"text/plain": ["np.float64(165.**************)"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["df_processing_time[\"processing_time_seconds\"].sum() / 116"]}, {"cell_type": "code", "execution_count": 10, "id": "184f8168", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['ime_account.id', 'ime_account.ime_id',\n", "       'ime_account.margin_account', 'ime_account.name',\n", "       'ime_account.number', 'date.end_date', 'date.id', 'date.ime_id',\n", "       'date.start_date', 'transaction.class', 'transaction.currency',\n", "       'transaction.exchange', 'transaction.id', 'transaction.ime_id',\n", "       'transaction.imeacc_id', 'transaction.investment_value',\n", "       'transaction.name', 'transaction.ps_id', 'transaction.psacc_id',\n", "       'transaction.security_unique_id', 'transaction.ticker',\n", "       'transaction.transaction_date', 'transaction.transaction_type',\n", "       'transaction.tt_id', 'transaction.type',\n", "       'investment_management_entity.address',\n", "       'investment_management_entity.entity_unique_id',\n", "       'investment_management_entity.id',\n", "       'investment_management_entity.name',\n", "       'investment_management_entity.website', 'holding.beginning_value',\n", "       'holding.class', 'holding.currency', 'holding.ending_value',\n", "       'holding.exchange', 'holding.ht_id', 'holding.id',\n", "       'holding.ime_id', 'holding.imeacc_id', 'holding.name',\n", "       'holding.ps_id', 'holding.psacc_id', 'holding.security_unique_id',\n", "       'holding.ticker', 'holding.type', 'ps_account.id',\n", "       'ps_account.imeacc_id', 'ps_account.name', 'ps_account.number',\n", "       'ps_account.ps_id', 'plan_sponsor.address',\n", "       'plan_sponsor.entity_unique_id', 'plan_sponsor.id',\n", "       'plan_sponsor.ime_id', 'plan_sponsor.name', 'plan_sponsor.website'],\n", "      dtype=object)"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["df[\"field\"].unique()"]}, {"cell_type": "code", "execution_count": 11, "id": "c9815ca7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Métricas por archivo:\n", "                                                 file  TP_Fuzzy  TN_Fuzzy  \\\n", "0   0abc6f33-69af-43b0-bf4a-2bc5d70d5a50_20240131 ...       156       534   \n", "1   0cc16af5-ab4e-4a63-80ce-3f24cf58c89c_20240930-...       455      3369   \n", "2   0dac8098-5ea5-4649-95fb-a3322bc1b290_2024-Q1_A...       263     10468   \n", "3   14e370a6-6e82-4cbc-aa8c-7660fee5b8bf_<PERSON>...        42        22   \n", "4   16b4c3cb-a242-4881-8186-a500c7efffd6_1024_Bett...       572      1545   \n", "5   23bf056d-881b-44bc-90e8-73d071222d10_20230101_...       336      2960   \n", "6   2ca35e68-4839-4e00-a83e-636367c9c2bd_09 30 202...       327       419   \n", "7   32d3f247-3048-46a0-a5d9-28c3f80f69e1_2023-10-3...       278      1382   \n", "8   3431e268-fd64-4665-bc73-1310a4027764_20250331_...       479      2648   \n", "9   4abfc941-1097-41f8-85b2-277c1df0e8f5_<PERSON>...        50        31   \n", "10  4dcb34cb-b0d9-4123-baae-5e48ddc3e1c7_<PERSON>...        47        39   \n", "11  5cdc7415-7142-4d47-8283-b2fde3669105_2024_Step...       287       697   \n", "12  5fef4ccb-642f-474a-978e-1b32fb9af9b7_Bettermen...       551       739   \n", "13  6ae6f3ff-b022-455e-a1e8-cc27919a10b5_2024_Sept...       505       856   \n", "14  74314224-0065-4fa8-a7fc-b948ffe2da60_202308 Be...        32        26   \n", "15  7e187f08-dde6-449a-8b67-a1d1ddaef580_Morgan St...        80        78   \n", "16  7f1712b8-7067-454e-9932-ae4be05f9826_<PERSON>...       203       209   \n", "17  81d60b06-0712-491a-9b2a-85b9c4696368_202401-20...        95       226   \n", "18  87f1e600-958d-4435-b50e-825009b31398_<PERSON>...       203       156   \n", "19  8ec19147-aa88-47f9-81c6-7dbbbebd22d1_2024-10 B...       517      2129   \n", "20  9523134b-02b5-46a9-87a8-d476f718771b_<PERSON>...        34        25   \n", "21  9715918a-a392-4b28-82fd-c5794215e1c7_<PERSON>..        56        53   \n", "22  9e766f85-ffae-4152-bc62-5b888629cd36_<PERSON>...        41        24   \n", "23  a7557258-e62f-4103-b848-3339d465b051_A<PERSON>y bet...       799      3755   \n", "24  b2df906b-851e-4f5f-a0af-c949ba9715b0_JP <PERSON>...       305       266   \n", "25  be63f115-77e6-4f86-9989-551ca9b70686_202409_Be...       539      2776   \n", "26  c3cfcaae-1488-43c4-a5cf-b8e1a622adc2_Multiple_...       197       983   \n", "27  c9054af5-f046-4d5a-90e6-74bc47520ebd_20241229 ...       171       293   \n", "28  cd9efafd-607b-4549-923e-580c3474b902_<PERSON>...      1456      5671   \n", "29  cebb5a95-009d-4a76-b74e-9aaf4dca85ea_<PERSON> M...        16         9   \n", "30  d2285642-6d32-4b9f-9511-767e2a660bb6_<PERSON>...       117        86   \n", "31  f050c995-97ef-4427-9d3c-b909407ffc9a_<PERSON>...       175       130   \n", "32  f25fc95d-df49-403f-b750-80c9ac432b39_<PERSON> Bag...       946     13648   \n", "33  f2ee0467-e6e3-429e-952c-4d1a363951bc_<PERSON> J...       154        71   \n", "34  f65d7997-a99c-445d-a104-de4d233a7259_2024 bett...       723      5401   \n", "35  fae97abe-9d9e-4fb4-8255-fc757dce8d73_<PERSON>...        62        37   \n", "36  ff8e4eeb-462d-416c-a208-8517c1c15315_morgan st...        19         9   \n", "\n", "    FP_Fuzzy  FN_Fuzzy  TP_Exact  TN_Exact  FP_Exact  FN_Exact  \\\n", "0         40       410       134       534        62       410   \n", "1        128      2756       403      3369       180      2756   \n", "2         86     12919       226     10468       123     12919   \n", "3         12         0        32        22        22         0   \n", "4        311      1085       398      1545       485      1085   \n", "5         35      2744       240      2960       131      2744   \n", "6        303       197       235       419       395       197   \n", "7         70      1056       245      1382       103      1056   \n", "8        119      1963       415      2648       183      1963   \n", "9         12         0        39        31        23         0   \n", "10        44         7        31        39        60         7   \n", "11        67       508       256       697        98       508   \n", "12       162       321       431       739       282       321   \n", "13       221       485       395       856       331       485   \n", "14        19         1        19        26        32         1   \n", "15        42         0        67        78        55         0   \n", "16        75        63       144       209       134        63   \n", "17        29        84        80       226        44        84   \n", "18        77        15       144       156       136        15   \n", "19       132      1508       417      2129       232      1508   \n", "20        10         7        27        25        17         7   \n", "21        16        13        43        53        29        13   \n", "22        11         0        29        24        23         0   \n", "23       256      2683       583      3755       472      2683   \n", "24       100        77       240       266       165        77   \n", "25       122      2080       447      2776       214      2080   \n", "26        72       714       119       983       150       714   \n", "27        99       183       144       293       126       183   \n", "28       503      3729       937      5671      1022      3729   \n", "29         9         1        14         9        11         1   \n", "30        55         7        78        86        94         7   \n", "31        68        14       135       130       108        14   \n", "32       396     11112       729     13648       613     11112   \n", "33       321         0       130        71       345         0   \n", "34       177      4297       576      5401       324      4297   \n", "35        23         1        53        37        32         1   \n", "36        12         0        14         9        17         0   \n", "\n", "    Precision_Fuzzy  Recall_Fuzzy  Precision_Exact  Recall_Exact  \\\n", "0          0.795918      0.275618         0.683673      0.246324   \n", "1          0.780446      0.141700         0.691252      0.127572   \n", "2          0.753582      0.019951         0.647564      0.017193   \n", "3          0.777778      1.000000         0.592593      1.000000   \n", "4          0.647792      0.345202         0.450736      0.268375   \n", "5          0.905660      0.109091         0.646900      0.080429   \n", "6          0.519048      0.624046         0.373016      0.543981   \n", "7          0.798851      0.208396         0.704023      0.188317   \n", "8          0.801003      0.196151         0.693980      0.174516   \n", "9          0.806452      1.000000         0.629032      1.000000   \n", "10         0.516484      0.870370         0.340659      0.815789   \n", "11         0.810734      0.361006         0.723164      0.335079   \n", "12         0.772791      0.631881         0.604488      0.573138   \n", "13         0.695592      0.510101         0.544077      0.448864   \n", "14         0.627451      0.969697         0.372549      0.950000   \n", "15         0.655738      1.000000         0.549180      1.000000   \n", "16         0.730216      0.763158         0.517986      0.695652   \n", "17         0.766129      0.530726         0.645161      0.487805   \n", "18         0.725000      0.931193         0.514286      0.905660   \n", "19         0.796610      0.255309         0.642527      0.216623   \n", "20         0.772727      0.829268         0.613636      0.794118   \n", "21         0.777778      0.811594         0.597222      0.767857   \n", "22         0.788462      1.000000         0.557692      1.000000   \n", "23         0.757346      0.229466         0.552607      0.178506   \n", "24         0.753086      0.798429         0.592593      0.757098   \n", "25         0.815431      0.205804         0.676248      0.176890   \n", "26         0.732342      0.216246         0.442379      0.142857   \n", "27         0.633333      0.483051         0.533333      0.440367   \n", "28         0.743236      0.280810         0.478305      0.200814   \n", "29         0.640000      0.941176         0.560000      0.933333   \n", "30         0.680233      0.943548         0.453488      0.917647   \n", "31         0.720165      0.925926         0.555556      0.906040   \n", "32         0.704918      0.078454         0.543219      0.061566   \n", "33         0.324211      1.000000         0.273684      1.000000   \n", "34         0.803333      0.144024         0.640000      0.118202   \n", "35         0.729412      0.984127         0.623529      0.981481   \n", "36         0.612903      1.000000         0.451613      1.000000   \n", "\n", "    Accuracy_Fuzzy  Accuracy_Exact  \n", "0         0.257426        0.221122  \n", "1         0.136268        0.120695  \n", "2         0.019822        0.017033  \n", "3         0.777778        0.592593  \n", "4         0.290650        0.202236  \n", "5         0.107865        0.077047  \n", "6         0.395405        0.284160  \n", "7         0.198006        0.174501  \n", "8         0.187036        0.162046  \n", "9         0.806452        0.629032  \n", "10        0.479592        0.316327  \n", "11        0.332947        0.296984  \n", "12        0.532882        0.416828  \n", "13        0.417011        0.326177  \n", "14        0.615385        0.365385  \n", "15        0.655738        0.549180  \n", "16        0.595308        0.422287  \n", "17        0.456731        0.384615  \n", "18        0.688136        0.488136  \n", "19        0.239685        0.193324  \n", "20        0.666667        0.529412  \n", "21        0.658824        0.505882  \n", "22        0.788462        0.557692  \n", "23        0.213751        0.155966  \n", "24        0.632780        0.497925  \n", "25        0.196644        0.163079  \n", "26        0.200407        0.121058  \n", "27        0.377483        0.317881  \n", "28        0.255977        0.164733  \n", "29        0.615385        0.538462  \n", "30        0.653631        0.435754  \n", "31        0.680934        0.525292  \n", "32        0.075960        0.058535  \n", "33        0.324211        0.273684  \n", "34        0.139119        0.110833  \n", "35        0.720930        0.616279  \n", "36        0.612903        0.451613  \n"]}], "source": ["# Agrupamos por 'file' y sumamos las métricas\n", "metrics_by_file = df.groupby('file')[['TP_Fuzzy', 'TN_Fuzzy', 'FP_Fuzzy', 'FN_Fuzzy', 'TP_Exact', 'TN_Exact', 'FP_Exact', 'FN_Exact']].sum().reset_index()\n", "\n", "# Calculamos las métricas finales\n", "metrics_by_file = calculate_final_metrics(metrics_by_file)\n", "\n", "print(\"Métricas por archivo:\")\n", "print(metrics_by_file)"]}, {"cell_type": "code", "execution_count": 12, "id": "411e5cda", "metadata": {}, "outputs": [], "source": ["# Agrupamos por 'field' y sumamos las métricas\n", "metrics_by_field = df.groupby('field')[['TP_Fuzzy', 'TN_Fuzzy', 'FP_Fuzzy', 'FN_Fuzzy', 'TP_Exact', 'TN_Exact', 'FP_Exact', 'FN_Exact']].sum().reset_index()\n", "\n", "# Calculamos las métricas finales\n", "metrics_by_field = calculate_final_metrics(metrics_by_field)"]}, {"cell_type": "code", "execution_count": 13, "id": "aa8d3d32", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>file</th>\n", "      <th>Accuracy_Fuzzy</th>\n", "      <th>Accuracy_Exact</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0abc6f33-69af-43b0-bf4a-2bc5d70d5a50_20240131 ...</td>\n", "      <td>0.257426</td>\n", "      <td>0.221122</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0cc16af5-ab4e-4a63-80ce-3f24cf58c89c_20240930-...</td>\n", "      <td>0.136268</td>\n", "      <td>0.120695</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0dac8098-5ea5-4649-95fb-a3322bc1b290_2024-Q1_A...</td>\n", "      <td>0.019822</td>\n", "      <td>0.017033</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>14e370a6-6e82-4cbc-aa8c-7660fee5b8bf_<PERSON>...</td>\n", "      <td>0.777778</td>\n", "      <td>0.592593</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>16b4c3cb-a242-4881-8186-a500c7efffd6_1024_Bett...</td>\n", "      <td>0.290650</td>\n", "      <td>0.202236</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>23bf056d-881b-44bc-90e8-73d071222d10_20230101_...</td>\n", "      <td>0.107865</td>\n", "      <td>0.077047</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2ca35e68-4839-4e00-a83e-636367c9c2bd_09 30 202...</td>\n", "      <td>0.395405</td>\n", "      <td>0.284160</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>32d3f247-3048-46a0-a5d9-28c3f80f69e1_2023-10-3...</td>\n", "      <td>0.198006</td>\n", "      <td>0.174501</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>3431e268-fd64-4665-bc73-1310a4027764_20250331_...</td>\n", "      <td>0.187036</td>\n", "      <td>0.162046</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>4abfc941-1097-41f8-85b2-277c1df0e8f5_<PERSON>...</td>\n", "      <td>0.806452</td>\n", "      <td>0.629032</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>4dcb34cb-b0d9-4123-baae-5e48ddc3e1c7_<PERSON> J...</td>\n", "      <td>0.479592</td>\n", "      <td>0.316327</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>5cdc7415-7142-4d47-8283-b2fde3669105_2024_Step...</td>\n", "      <td>0.332947</td>\n", "      <td>0.296984</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>5fef4ccb-642f-474a-978e-1b32fb9af9b7_Bettermen...</td>\n", "      <td>0.532882</td>\n", "      <td>0.416828</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>6ae6f3ff-b022-455e-a1e8-cc27919a10b5_2024_Sept...</td>\n", "      <td>0.417011</td>\n", "      <td>0.326177</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>74314224-0065-4fa8-a7fc-b948ffe2da60_202308 Be...</td>\n", "      <td>0.615385</td>\n", "      <td>0.365385</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>7e187f08-dde6-449a-8b67-a1d1ddaef580_Morgan St...</td>\n", "      <td>0.655738</td>\n", "      <td>0.549180</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>7f1712b8-7067-454e-9932-ae4be05f9826_<PERSON>...</td>\n", "      <td>0.595308</td>\n", "      <td>0.422287</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>81d60b06-0712-491a-9b2a-85b9c4696368_202401-20...</td>\n", "      <td>0.456731</td>\n", "      <td>0.384615</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>87f1e600-958d-4435-b50e-825009b31398_<PERSON>...</td>\n", "      <td>0.688136</td>\n", "      <td>0.488136</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>8ec19147-aa88-47f9-81c6-7dbbbebd22d1_2024-10 B...</td>\n", "      <td>0.239685</td>\n", "      <td>0.193324</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>9523134b-02b5-46a9-87a8-d476f718771b_<PERSON>...</td>\n", "      <td>0.666667</td>\n", "      <td>0.529412</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>9715918a-a392-4b28-82fd-c5794215e1c7_<PERSON> J...</td>\n", "      <td>0.658824</td>\n", "      <td>0.505882</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>9e766f85-ffae-4152-bc62-5b888629cd36_<PERSON> Morgan...</td>\n", "      <td>0.788462</td>\n", "      <td>0.557692</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>a7557258-e62f-4103-b848-3339d465b051_A<PERSON>y bet...</td>\n", "      <td>0.213751</td>\n", "      <td>0.155966</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>b2df906b-851e-4f5f-a0af-c949ba9715b0_JP Morgan...</td>\n", "      <td>0.632780</td>\n", "      <td>0.497925</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>be63f115-77e6-4f86-9989-551ca9b70686_202409_Be...</td>\n", "      <td>0.196644</td>\n", "      <td>0.163079</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>c3cfcaae-1488-43c4-a5cf-b8e1a622adc2_Multiple_...</td>\n", "      <td>0.200407</td>\n", "      <td>0.121058</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>c9054af5-f046-4d5a-90e6-74bc47520ebd_20241229 ...</td>\n", "      <td>0.377483</td>\n", "      <td>0.317881</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>cd9efafd-607b-4549-923e-580c3474b902_<PERSON>...</td>\n", "      <td>0.255977</td>\n", "      <td>0.164733</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>cebb5a95-009d-4a76-b74e-9aaf4dca85ea_<PERSON> M<PERSON>.</td>\n", "      <td>0.615385</td>\n", "      <td>0.538462</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>d2285642-6d32-4b9f-9511-767e2a660bb6_<PERSON>...</td>\n", "      <td>0.653631</td>\n", "      <td>0.435754</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>f050c995-97ef-4427-9d3c-b909407ffc9a_<PERSON>...</td>\n", "      <td>0.680934</td>\n", "      <td>0.525292</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>f25fc95d-df49-403f-b750-80c9ac432b39_<PERSON> Bag...</td>\n", "      <td>0.075960</td>\n", "      <td>0.058535</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>f2ee0467-e6e3-429e-952c-4d1a363951bc_<PERSON> J...</td>\n", "      <td>0.324211</td>\n", "      <td>0.273684</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>f65d7997-a99c-445d-a104-de4d233a7259_2024 bett...</td>\n", "      <td>0.139119</td>\n", "      <td>0.110833</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35</th>\n", "      <td>fae97abe-9d9e-4fb4-8255-fc757dce8d73_<PERSON>...</td>\n", "      <td>0.720930</td>\n", "      <td>0.616279</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>ff8e4eeb-462d-416c-a208-8517c1c15315_morgan st...</td>\n", "      <td>0.612903</td>\n", "      <td>0.451613</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                                 file  Accuracy_Fuzzy  \\\n", "0   0abc6f33-69af-43b0-bf4a-2bc5d70d5a50_20240131 ...        0.257426   \n", "1   0cc16af5-ab4e-4a63-80ce-3f24cf58c89c_20240930-...        0.136268   \n", "2   0dac8098-5ea5-4649-95fb-a3322bc1b290_2024-Q1_A...        0.019822   \n", "3   14e370a6-6e82-4cbc-aa8c-7660fee5b8bf_<PERSON> Morgan...        0.777778   \n", "4   16b4c3cb-a242-4881-8186-a500c7efffd6_1024_Bett...        0.290650   \n", "5   23bf056d-881b-44bc-90e8-73d071222d10_20230101_...        0.107865   \n", "6   2ca35e68-4839-4e00-a83e-636367c9c2bd_09 30 202...        0.395405   \n", "7   32d3f247-3048-46a0-a5d9-28c3f80f69e1_2023-10-3...        0.198006   \n", "8   3431e268-fd64-4665-bc73-1310a4027764_20250331_...        0.187036   \n", "9   4abfc941-1097-41f8-85b2-277c1df0e8f5_<PERSON>...        0.806452   \n", "10  4dcb34cb-b0d9-4123-baae-5e48ddc3e1c7_<PERSON> J...        0.479592   \n", "11  5cdc7415-7142-4d47-8283-b2fde3669105_2024_Step...        0.332947   \n", "12  5fef4ccb-642f-474a-978e-1b32fb9af9b7_Bettermen...        0.532882   \n", "13  6ae6f3ff-b022-455e-a1e8-cc27919a10b5_2024_Sept...        0.417011   \n", "14  74314224-0065-4fa8-a7fc-b948ffe2da60_202308 Be...        0.615385   \n", "15  7e187f08-dde6-449a-8b67-a1d1ddaef580_Morgan St...        0.655738   \n", "16  7f1712b8-7067-454e-9932-ae4be05f9826_<PERSON> E...        0.595308   \n", "17  81d60b06-0712-491a-9b2a-85b9c4696368_202401-20...        0.456731   \n", "18  87f1e600-958d-4435-b50e-825009b31398_<PERSON>...        0.688136   \n", "19  8ec19147-aa88-47f9-81c6-7dbbbebd22d1_2024-10 B...        0.239685   \n", "20  9523134b-02b5-46a9-87a8-d476f718771b_<PERSON>...        0.666667   \n", "21  9715918a-a392-4b28-82fd-c5794215e1c7_<PERSON> J...        0.658824   \n", "22  9e766f85-ffae-4152-bc62-5b888629cd36_<PERSON> Morgan...        0.788462   \n", "23  a7557258-e62f-4103-b848-3339d465b051_A<PERSON>y bet...        0.213751   \n", "24  b2df906b-851e-4f5f-a0af-c949ba9715b0_JP Morgan...        0.632780   \n", "25  be63f115-77e6-4f86-9989-551ca9b70686_202409_Be...        0.196644   \n", "26  c3cfcaae-1488-43c4-a5cf-b8e1a622adc2_Multiple_...        0.200407   \n", "27  c9054af5-f046-4d5a-90e6-74bc47520ebd_20241229 ...        0.377483   \n", "28  cd9efafd-607b-4549-923e-580c3474b902_<PERSON>...        0.255977   \n", "29  cebb5a95-009d-4a76-b74e-9aaf4dca85ea_<PERSON> M...        0.615385   \n", "30  d2285642-6d32-4b9f-9511-767e2a660bb6_<PERSON>...        0.653631   \n", "31  f050c995-97ef-4427-9d3c-b909407ffc9a_<PERSON>...        0.680934   \n", "32  f25fc95d-df49-403f-b750-80c9ac432b39_<PERSON> Bag...        0.075960   \n", "33  f2ee0467-e6e3-429e-952c-4d1a363951bc_<PERSON> J...        0.324211   \n", "34  f65d7997-a99c-445d-a104-de4d233a7259_2024 bett...        0.139119   \n", "35  fae97abe-9d9e-4fb4-8255-fc757dce8d73_<PERSON>...        0.720930   \n", "36  ff8e4eeb-462d-416c-a208-8517c1c15315_morgan st...        0.612903   \n", "\n", "    Accuracy_Exact  \n", "0         0.221122  \n", "1         0.120695  \n", "2         0.017033  \n", "3         0.592593  \n", "4         0.202236  \n", "5         0.077047  \n", "6         0.284160  \n", "7         0.174501  \n", "8         0.162046  \n", "9         0.629032  \n", "10        0.316327  \n", "11        0.296984  \n", "12        0.416828  \n", "13        0.326177  \n", "14        0.365385  \n", "15        0.549180  \n", "16        0.422287  \n", "17        0.384615  \n", "18        0.488136  \n", "19        0.193324  \n", "20        0.529412  \n", "21        0.505882  \n", "22        0.557692  \n", "23        0.155966  \n", "24        0.497925  \n", "25        0.163079  \n", "26        0.121058  \n", "27        0.317881  \n", "28        0.164733  \n", "29        0.538462  \n", "30        0.435754  \n", "31        0.525292  \n", "32        0.058535  \n", "33        0.273684  \n", "34        0.110833  \n", "35        0.616279  \n", "36        0.451613  "]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["metrics_by_file[[\"file\", 'Accuracy_Fuzzy', 'Accuracy_Exact']]"]}, {"cell_type": "code", "execution_count": 14, "id": "96ba27d2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Métricas agregadas por campo:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>field</th>\n", "      <th>Accuracy_Fuzzy</th>\n", "      <th>Accuracy_Exact</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>date.end_date</td>\n", "      <td>0.880952</td>\n", "      <td>0.619048</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>date.id</td>\n", "      <td>0.880952</td>\n", "      <td>0.857143</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>date.ime_id</td>\n", "      <td>0.880952</td>\n", "      <td>0.857143</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>date.start_date</td>\n", "      <td>0.857143</td>\n", "      <td>0.595238</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>holding.beginning_value</td>\n", "      <td>0.395597</td>\n", "      <td>0.394930</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>holding.class</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>holding.currency</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>holding.ending_value</td>\n", "      <td>0.457878</td>\n", "      <td>0.429582</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>holding.exchange</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>holding.ht_id</td>\n", "      <td>0.460568</td>\n", "      <td>0.164038</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>holding.id</td>\n", "      <td>0.460568</td>\n", "      <td>0.377918</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>holding.ime_id</td>\n", "      <td>0.460568</td>\n", "      <td>0.459306</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>holding.imeacc_id</td>\n", "      <td>0.452411</td>\n", "      <td>0.421954</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>holding.name</td>\n", "      <td>0.460568</td>\n", "      <td>0.415773</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>holding.ps_id</td>\n", "      <td>0.157407</td>\n", "      <td>0.157407</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>holding.psacc_id</td>\n", "      <td>0.157407</td>\n", "      <td>0.157407</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>holding.security_unique_id</td>\n", "      <td>0.250000</td>\n", "      <td>0.250000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>holding.ticker</td>\n", "      <td>0.472924</td>\n", "      <td>0.464982</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>holding.type</td>\n", "      <td>0.035242</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>ime_account.id</td>\n", "      <td>0.891566</td>\n", "      <td>0.891566</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>ime_account.ime_id</td>\n", "      <td>0.891566</td>\n", "      <td>0.879518</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>ime_account.margin_account</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>ime_account.name</td>\n", "      <td>0.837500</td>\n", "      <td>0.662500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>ime_account.number</td>\n", "      <td>0.890244</td>\n", "      <td>0.890244</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>investment_management_entity.address</td>\n", "      <td>0.804878</td>\n", "      <td>0.024390</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>investment_management_entity.entity_unique_id</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>investment_management_entity.id</td>\n", "      <td>0.860465</td>\n", "      <td>0.860465</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>investment_management_entity.name</td>\n", "      <td>0.785714</td>\n", "      <td>0.023810</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>investment_management_entity.website</td>\n", "      <td>0.485714</td>\n", "      <td>0.342857</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>plan_sponsor.address</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>plan_sponsor.entity_unique_id</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>plan_sponsor.id</td>\n", "      <td>0.300000</td>\n", "      <td>0.300000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>plan_sponsor.ime_id</td>\n", "      <td>0.333333</td>\n", "      <td>0.333333</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>plan_sponsor.name</td>\n", "      <td>0.333333</td>\n", "      <td>0.333333</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>plan_sponsor.website</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35</th>\n", "      <td>ps_account.id</td>\n", "      <td>0.230769</td>\n", "      <td>0.230769</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>ps_account.imeacc_id</td>\n", "      <td>0.153846</td>\n", "      <td>0.076923</td>\n", "    </tr>\n", "    <tr>\n", "      <th>37</th>\n", "      <td>ps_account.name</td>\n", "      <td>0.153846</td>\n", "      <td>0.153846</td>\n", "    </tr>\n", "    <tr>\n", "      <th>38</th>\n", "      <td>ps_account.number</td>\n", "      <td>0.300000</td>\n", "      <td>0.300000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39</th>\n", "      <td>ps_account.ps_id</td>\n", "      <td>0.153846</td>\n", "      <td>0.153846</td>\n", "    </tr>\n", "    <tr>\n", "      <th>40</th>\n", "      <td>transaction.class</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>41</th>\n", "      <td>transaction.currency</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>42</th>\n", "      <td>transaction.exchange</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>43</th>\n", "      <td>transaction.id</td>\n", "      <td>0.101002</td>\n", "      <td>0.032382</td>\n", "    </tr>\n", "    <tr>\n", "      <th>44</th>\n", "      <td>transaction.ime_id</td>\n", "      <td>0.095253</td>\n", "      <td>0.094938</td>\n", "    </tr>\n", "    <tr>\n", "      <th>45</th>\n", "      <td>transaction.imeacc_id</td>\n", "      <td>0.095253</td>\n", "      <td>0.062766</td>\n", "    </tr>\n", "    <tr>\n", "      <th>46</th>\n", "      <td>transaction.investment_value</td>\n", "      <td>0.097188</td>\n", "      <td>0.066440</td>\n", "    </tr>\n", "    <tr>\n", "      <th>47</th>\n", "      <td>transaction.name</td>\n", "      <td>0.082780</td>\n", "      <td>0.037066</td>\n", "    </tr>\n", "    <tr>\n", "      <th>48</th>\n", "      <td>transaction.ps_id</td>\n", "      <td>0.135870</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>49</th>\n", "      <td>transaction.psacc_id</td>\n", "      <td>0.135870</td>\n", "      <td>0.135870</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50</th>\n", "      <td>transaction.security_unique_id</td>\n", "      <td>0.181818</td>\n", "      <td>0.181818</td>\n", "    </tr>\n", "    <tr>\n", "      <th>51</th>\n", "      <td>transaction.ticker</td>\n", "      <td>0.188401</td>\n", "      <td>0.177419</td>\n", "    </tr>\n", "    <tr>\n", "      <th>52</th>\n", "      <td>transaction.transaction_date</td>\n", "      <td>0.100709</td>\n", "      <td>0.076033</td>\n", "    </tr>\n", "    <tr>\n", "      <th>53</th>\n", "      <td>transaction.transaction_type</td>\n", "      <td>0.031791</td>\n", "      <td>0.025713</td>\n", "    </tr>\n", "    <tr>\n", "      <th>54</th>\n", "      <td>transaction.tt_id</td>\n", "      <td>0.101002</td>\n", "      <td>0.050887</td>\n", "    </tr>\n", "    <tr>\n", "      <th>55</th>\n", "      <td>transaction.type</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                            field  Accuracy_Fuzzy  \\\n", "0                                   date.end_date        0.880952   \n", "1                                         date.id        0.880952   \n", "2                                     date.ime_id        0.880952   \n", "3                                 date.start_date        0.857143   \n", "4                         holding.beginning_value        0.395597   \n", "5                                   holding.class        0.000000   \n", "6                                holding.currency        0.000000   \n", "7                            holding.ending_value        0.457878   \n", "8                                holding.exchange        0.000000   \n", "9                                   holding.ht_id        0.460568   \n", "10                                     holding.id        0.460568   \n", "11                                 holding.ime_id        0.460568   \n", "12                              holding.imeacc_id        0.452411   \n", "13                                   holding.name        0.460568   \n", "14                                  holding.ps_id        0.157407   \n", "15                               holding.psacc_id        0.157407   \n", "16                     holding.security_unique_id        0.250000   \n", "17                                 holding.ticker        0.472924   \n", "18                                   holding.type        0.035242   \n", "19                                 ime_account.id        0.891566   \n", "20                             ime_account.ime_id        0.891566   \n", "21                     ime_account.margin_account        0.000000   \n", "22                               ime_account.name        0.837500   \n", "23                             ime_account.number        0.890244   \n", "24           investment_management_entity.address        0.804878   \n", "25  investment_management_entity.entity_unique_id        0.000000   \n", "26                investment_management_entity.id        0.860465   \n", "27              investment_management_entity.name        0.785714   \n", "28           investment_management_entity.website        0.485714   \n", "29                           plan_sponsor.address        0.000000   \n", "30                  plan_sponsor.entity_unique_id        0.000000   \n", "31                                plan_sponsor.id        0.300000   \n", "32                            plan_sponsor.ime_id        0.333333   \n", "33                              plan_sponsor.name        0.333333   \n", "34                           plan_sponsor.website        0.000000   \n", "35                                  ps_account.id        0.230769   \n", "36                           ps_account.imeacc_id        0.153846   \n", "37                                ps_account.name        0.153846   \n", "38                              ps_account.number        0.300000   \n", "39                               ps_account.ps_id        0.153846   \n", "40                              transaction.class        0.000000   \n", "41                           transaction.currency        0.000000   \n", "42                           transaction.exchange        0.000000   \n", "43                                 transaction.id        0.101002   \n", "44                             transaction.ime_id        0.095253   \n", "45                          transaction.imeacc_id        0.095253   \n", "46                   transaction.investment_value        0.097188   \n", "47                               transaction.name        0.082780   \n", "48                              transaction.ps_id        0.135870   \n", "49                           transaction.psacc_id        0.135870   \n", "50                 transaction.security_unique_id        0.181818   \n", "51                             transaction.ticker        0.188401   \n", "52                   transaction.transaction_date        0.100709   \n", "53                   transaction.transaction_type        0.031791   \n", "54                              transaction.tt_id        0.101002   \n", "55                               transaction.type        0.000000   \n", "\n", "    Accuracy_Exact  \n", "0         0.619048  \n", "1         0.857143  \n", "2         0.857143  \n", "3         0.595238  \n", "4         0.394930  \n", "5         0.000000  \n", "6         0.000000  \n", "7         0.429582  \n", "8         0.000000  \n", "9         0.164038  \n", "10        0.377918  \n", "11        0.459306  \n", "12        0.421954  \n", "13        0.415773  \n", "14        0.157407  \n", "15        0.157407  \n", "16        0.250000  \n", "17        0.464982  \n", "18        0.000000  \n", "19        0.891566  \n", "20        0.879518  \n", "21        0.000000  \n", "22        0.662500  \n", "23        0.890244  \n", "24        0.024390  \n", "25        0.000000  \n", "26        0.860465  \n", "27        0.023810  \n", "28        0.342857  \n", "29        0.000000  \n", "30        0.000000  \n", "31        0.300000  \n", "32        0.333333  \n", "33        0.333333  \n", "34        0.000000  \n", "35        0.230769  \n", "36        0.076923  \n", "37        0.153846  \n", "38        0.300000  \n", "39        0.153846  \n", "40        0.000000  \n", "41        0.000000  \n", "42        0.000000  \n", "43        0.032382  \n", "44        0.094938  \n", "45        0.062766  \n", "46        0.066440  \n", "47        0.037066  \n", "48        0.000000  \n", "49        0.135870  \n", "50        0.181818  \n", "51        0.177419  \n", "52        0.076033  \n", "53        0.025713  \n", "54        0.050887  \n", "55        0.000000  "]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["# Agrupamos por 'field' y sumamos las métricas\n", "metrics_by_field = df.groupby('field')[['TP_Fuzzy', 'TN_Fuzzy', 'FP_Fuzzy', 'FN_Fuzzy', 'TP_Exact', 'TN_Exact', 'FP_Exact', 'FN_Exact']].sum().reset_index()\n", "\n", "# Calculamos las métricas finalese3\n", "metrics_by_field = calculate_final_metrics(metrics_by_field)\n", "\n", "print(\"\\nMétricas agregadas por campo:\")\n", "metrics_by_field[[\"field\", 'Accuracy_Fuzzy', 'Accuracy_Exact']]"]}, {"cell_type": "code", "execution_count": 15, "id": "ad7888b9", "metadata": {}, "outputs": [], "source": ["def get_total_metrics_for_selected_fields(original_df, selected_fields):\n", "    \"\"\"\n", "    Calcula las métricas totales para una lista de campos seleccionados.\n", "\n", "    Args:\n", "        original_df (pd.DataFrame): El DataFrame original con todos los datos.\n", "        selected_fields (list): Una lista de strings con los nombres de los campos a incluir.\n", "    \"\"\"\n", "    filtered_df = original_df[original_df['field'].isin(selected_fields)]\n", "\n", "    # Sumamos las métricas de los campos filtrados\n", "    total_metrics = filtered_df[['TP_Fuzzy', 'TN_Fuzzy', 'FP_Fuzzy', 'FN_Fuzzy', 'TP_Exact', 'TN_Exact', 'FP_Exact', 'FN_Exact']].sum().to_frame().T\n", "\n", "    total_metrics = calculate_final_metrics(total_metrics)\n", "\n", "    return total_metrics"]}, {"cell_type": "code", "execution_count": 18, "id": "3f8c7f83", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Métricas totales para todos los campos de 'holding':\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>TP_Fuzzy</th>\n", "      <th>T<PERSON>_Fuzzy</th>\n", "      <th>FP_Fuzzy</th>\n", "      <th><PERSON><PERSON>_Fuzzy</th>\n", "      <th>TP_Exact</th>\n", "      <th>TN_Exact</th>\n", "      <th>FP_Exact</th>\n", "      <th>FN_Exact</th>\n", "      <th>Precision_Fuzzy</th>\n", "      <th><PERSON><PERSON><PERSON>_<PERSON>zzy</th>\n", "      <th>Precision_Exact</th>\n", "      <th>Recall_Exact</th>\n", "      <th>Accuracy_Fuzzy</th>\n", "      <th>Accuracy_Exact</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>5655</td>\n", "      <td>9683</td>\n", "      <td>1868</td>\n", "      <td>6521</td>\n", "      <td>4853</td>\n", "      <td>9683</td>\n", "      <td>2670</td>\n", "      <td>6521</td>\n", "      <td>0.751695</td>\n", "      <td>0.464438</td>\n", "      <td>0.645088</td>\n", "      <td>0.426675</td>\n", "      <td>0.402663</td>\n", "      <td>0.345557</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   TP_Fuzzy  TN_Fuzzy  FP_Fuzzy  FN_Fuzzy  TP_Exact  TN_Exact  FP_Exact  \\\n", "0      5655      9683      1868      6521      4853      9683      2670   \n", "\n", "   FN_Exact  Precision_Fuzzy  Recall_Fuzzy  Precision_Exact  Recall_Exact  \\\n", "0      6521         0.751695      0.464438         0.645088      0.426675   \n", "\n", "   Accuracy_Fuzzy  Accuracy_Exact  \n", "0        0.402663        0.345557  "]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["fields_to_calculate = ['ime_account.id', 'ime_account.ime_id',\n", "       'ime_account.margin_account', 'ime_account.name',\n", "       'ime_account.number', 'date.end_date', 'date.id', 'date.ime_id',\n", "       'date.start_date', 'investment_management_entity.address',\n", "       'investment_management_entity.entity_unique_id',\n", "       'investment_management_entity.id',\n", "       'investment_management_entity.name',\n", "       'investment_management_entity.website', 'holding.beginning_value',\n", "       'holding.class', 'holding.currency', 'holding.ending_value',\n", "       'holding.exchange', 'holding.ht_id', 'holding.id',\n", "       'holding.ime_id', 'holding.imeacc_id', 'holding.name',\n", "       'holding.ps_id', 'holding.psacc_id', 'holding.security_unique_id',\n", "       'holding.ticker', 'holding.type', 'transaction.class',\n", "       'transaction.currency', 'transaction.exchange', 'transaction.id',\n", "       'transaction.ime_id', 'transaction.imeacc_id',\n", "       'transaction.investment_value', 'transaction.name',\n", "       'transaction.ps_id', 'transaction.psacc_id',\n", "       'transaction.security_unique_id', 'transaction.ticker',\n", "       'transaction.transaction_date', 'transaction.transaction_type',\n", "       'transaction.tt_id', 'transaction.type', 'plan_sponsor.address',\n", "       'plan_sponsor.entity_unique_id', 'plan_sponsor.id',\n", "       'plan_sponsor.ime_id', 'plan_sponsor.name', 'plan_sponsor.website',\n", "       'ps_account.id', 'ps_account.imeacc_id', 'ps_account.name',\n", "       'ps_account.number', 'ps_account.ps_id']\n", "\n", "total_selected_metrics = get_total_metrics_for_selected_fields(df, fields_to_calculate)\n", "\n", "holding_fields = [field for field in df['field'].unique() if (field.startswith('holding.'))]\n", "total_holding_metrics = get_total_metrics_for_selected_fields(df, holding_fields)\n", "\n", "print(f\"\\nMétricas totales para todos los campos de 'holding':\")\n", "total_holding_metrics"]}, {"cell_type": "code", "execution_count": 19, "id": "eb66a501", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>TP_Fuzzy</th>\n", "      <th>T<PERSON>_Fuzzy</th>\n", "      <th>FP_Fuzzy</th>\n", "      <th><PERSON><PERSON>_Fuzzy</th>\n", "      <th>TP_Exact</th>\n", "      <th>TN_Exact</th>\n", "      <th>FP_Exact</th>\n", "      <th>FN_Exact</th>\n", "      <th>Precision_Fuzzy</th>\n", "      <th><PERSON><PERSON><PERSON>_<PERSON>zzy</th>\n", "      <th>Precision_Exact</th>\n", "      <th>Recall_Exact</th>\n", "      <th>Accuracy_Fuzzy</th>\n", "      <th>Accuracy_Exact</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>11288</td>\n", "      <td>61770</td>\n", "      <td>4234</td>\n", "      <td>51040</td>\n", "      <td>8649</td>\n", "      <td>61770</td>\n", "      <td>6873</td>\n", "      <td>51040</td>\n", "      <td>0.727226</td>\n", "      <td>0.181106</td>\n", "      <td>0.557209</td>\n", "      <td>0.144901</td>\n", "      <td>0.169586</td>\n", "      <td>0.129939</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   TP_Fuzzy  TN_Fuzzy  FP_Fuzzy  FN_Fuzzy  TP_Exact  TN_Exact  FP_Exact  \\\n", "0     11288     61770      4234     51040      8649     61770      6873   \n", "\n", "   FN_Exact  Precision_Fuzzy  Recall_Fuzzy  Precision_Exact  Recall_Exact  \\\n", "0     51040         0.727226      0.181106         0.557209      0.144901   \n", "\n", "   Accuracy_Fuzzy  Accuracy_Exact  \n", "0        0.169586        0.129939  "]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["total_selected_metrics"]}, {"cell_type": "code", "execution_count": null, "id": "6f45069a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f5fdfda0", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 5}