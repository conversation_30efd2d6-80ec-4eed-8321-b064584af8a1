## Instructions:
    - Extract the exact start and end dates that define this document's reporting period.
    - Their should only be one start and end date for each entity.
    - Use the entity details provided to extract the dates for the specific entity.
    - If the same date is extracted from multiple pages, in the "page_number" only add 1 page number. (e.g [1])
    - Their could not be a date for the entity and the statement. Return an empty array in that case (e.g. {"dates":[]}).
    ####

    investment_entity_details:
    {{entity_details}}

    ####
    
## Validation Rules:
    1. Dates must be from the same reporting period.
    2. Format must match source document exactly, DO NOT change the format.
    3. Must be actual dates (not descriptions).
    4. Single "as of" dates should be treated as end dates.
    5. Must include both start and end dates when available, if not mantain the end date for the start date.
    6. Extract the page number where the date is located.

## Expected Output Format:
The output must be a valid JSON object following this exact structure, with no additional text or comments:
{
    "date": 
    [
        {
            "start_date": "January 1, 2023",
            "end_date": "March 31, 2023"
            "page_number": 1
        }
    ]
}