Extract investment management entities from financial statements. According to these specifications:

KEY CONCEPTS:
- Investment management entities are financial institutions managing investment accounts
- Each entity must have complete identification and account information.
- NO INFORMATION SHOULD BE INVENTED - use only explicit data from the document.

ENTITY IDENTIFICATION:
1. Valid Entities:
- Financial institutions (e.g., banks, brokerages)
- Insurance companies
- Investment management firms
- DO NOT INCLUDE: "Jonas Wealth Management" or similar advisory firms


2. Entity Location in Document:
- Headers and footers
- Account summary sections
- Statement overview pages
- Legal disclaimers

3. CRITICAL RULES:
    - If their are holdings or transactions in the current statement without a entity asigned choose the best word or phrase that describes and group them.
    - The word or phrase should be extracted from the statement this would be placeholders. Do not invent or modify the content.
    - Assign "True" to the is_placeholder variable

MANDATORY FIELDS (Can not be null the value must be present):

1. Entity Name:
- Official institution name only
- Exclude descriptive text and marketing taglines
- Must be a recognized financial institution

2. page_number: 
    - The page number where the entity information is located.

3. is_placeholder: If the entity is a placeholder set this to True. Else False. Only True or False. It needs to be boolean.

FIELDS TO NULL if not present:

1. Website:
    - It has to be a valid URL. Usually similar to the name of the entity.

2. Address:
- Do not use the customer address, only the physical location of the entity
- Exclude phone numbers and emails
- Format as shown in document

3. entity_unique_id: can be the 2 of the following values:
    - DUNS:
        - 9 Digit id with the Data Universal Numbering System managed by Dun & Bradstreet (D&B)
    - TaxID:
        - A Taxpayer Identification Number (TIN) is an identification number used by the Internal Revenue Service (IRS) in the administration of tax laws. 

Expected Output Format:
The output must be a valid JSON object following this exact structure, with no additional text or comments:
{
    "investment_management_entity": [
        {
            "name": "Fidelity Investments",
            "address": "900 Salem Street, Smithfield, RI 02917",
            "website": "www.fidelity.com",
            "entity_unique_id": "*********",
            "page_number": 1,
            "is_placeholder": False
        }
    ]
}