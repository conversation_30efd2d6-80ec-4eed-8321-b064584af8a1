Extract all holdings securities from the provided mardown.

KEY CONCEPTS:
- Holdings represent current securities/assets/equities owned at a specific point in time
- Each holding must have complete identification and value information
- Focus ONLY on actual holdings, not transactions or summaries

CRITICAL RULES:
1. NO VALUES SHOULD BE INVENTED OR INFERRED - use only explicit information from text
2. Extract holdings from BOTH table content AND column titles
3. Process complete tables including continuations across pages
4. If you do not find the value for the investment_beginning_value or investment_ending_value, leave it null.

TABLE IDENTIFICATION:
1. Process tables with these titles (case-insensitive):
- "EQUITIES"
- "SECURITIES"
- "ASSETS"
- "HOLDINGS"
- Any variations (e.g., "EQUITIES (continued)")
2. Look for holdings in:
- Main table content
- Description columns
- Related footnotes
3. EXCLUDE:
- Summary sections
- Aggregate totals
- Projected values
- Holdings for other entities
- Tables where the title has "Activities" in it. (case-insensitive)

MANDATORY FIELDS can not be null:
- ht_id: Extract the current table id from the markdown. Do not infer it or invent it.

OPTIONAL FIELDS (use null if missing):
- name: Security name include all the surrounding information.
- class: Must be one of this values: ["Shares", "Debt", "Funds", "Government", "InsPenFunds" (Insurance or Pension Funds), "Open-End Fund", "Other"]. Think twice before you decide.
- type: security type (e.g. Equity Closed Fund, Trust Preferred Securities, Bills) It depends on the class its classified. Think twice before you decide.
- ticker: unique series of letters (and sometimes numbers) assigned to a publicly traded security for trading and identification purposes. Usually inside the same table the holding is being extracted (only if explicit)
- exchange: Trading marketplace (only if explicit)
- currency: ISO 4217 code (if explicit or clearly inferable)
- beginning_value: Start value as string include also the sign if its substracted or added (exactly as in text).
- ending_value: End value as string include also the sign if its substracted or added (exactly as in text).
- security_unique_id can be the 3 of the following values:
    - isin: 12-character alphanumeric identifier
    - cusip: 9-character alphanumeric identifier
    - other: First found identifier from: ["AMFI", "SEDOL", "LipperID", "CINS", "VALOR", "APIR", "WKN", "CIN", "BD", "BLC", "CHCCODEFE", "CHNCODEBE", "CHNCODEFE", "COMMONCODE", "FUNDSERV", "IDNSECCODE", "ISMA", "ITA", "JASDA", "KOFIA", "SICC", "SICOVAM", "SVM", "THASECCODE", "TWNCODE", "WERTPAPIER"]
- ime_id: Extract the investment management entity id from the markdown. Do not infer it or invent it. Can be null.
- ps_id: Extract the plan sponsor entity id from the markdown. Do not infer it or invent it. Can be null.
- imeacc_id: Extract the investment management entity account id from the markdown. Do not infer it or invent it. Can be null.
- psacc_id: Extract the plan sponsor entity account id from the markdown. Do not infer it or invent it. Can be null.
- page_number: Extract the page number where the holding is located. Use the table page num as value.

#### OUTPUT FORMAT:
Must be valid JSON matching this structure exactly:
{
    "holding": [
        {
            "name": "Security Name",
            "ht_id": "table_id",
            "ticker": "SYMBOL",
            "class": "ValidClass",
            "type": "SecurityType",
            "exchange": "Market",
            "currency": "ISO",
            "beginning_value": "InvestmentBeginningValue",
            "ending_value": "investmentEndingValue",
            "security_unique_id": "12CharCode",
            "ime_id": "investment_management_entity_id",
            "ps_id": "plan_sponsor_entity_id"
            "imeacc_id": "ime_account_id",
            "psacc_id": "pse_account_id",
            "page_number": 1
        }
    ]
}

#### INPUT MARKDOWN DOCUMENT
{{chunk}}