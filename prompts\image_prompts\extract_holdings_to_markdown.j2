{# prompts/extract_holdings_to_markdown.j2 #}
Extract all holdings securities from the provided document. Do not include transactions or summaries tables.

Use the following information to extract the holdings:
- investment entity id: {{ entity_id }}
- investment entity name: {{ name }}
- investment entity account_type name: {{ ime_account_type }}
- investment entity account id: {{ ime_account_id }}
- investment entity account descriptor: {{ ime_descriptor }}
- plan sponsor entity id: {{ sponsor_id }}
- plan sponsor entity name: {{ sponsor_name }}
- plan sponsor entity account_type name: {{ plan_account }}
- plan sponsor entity descriptor: {{ plan_descriptor }}
- plan sponsor entity account_number: {{ plan_number }}
- plan sponsor entity account id: {{ pse_account_id }}

{% if plan_account %}
Only Extract the holdings correspondings to the plan sponsor entity type: {{ plan_account }}
{% endif %}

KEY CONCEPTS:
- Holdings represent current securities/assets/equities owned at a specific point in time
- Each holding must have complete identification and value information
- Focus ONLY on actual holdings, not transactions or summaries
- Identify the holdings tables (tables with the security information in the document) and asign an id to each of them (e.g. "1", "2"): {{ table_id }} + (The id given)

CRITICAL RULES:
1. NO VALUES SHOULD BE INVENTED OR INFERRED - use only explicit information from text
2. Extract holdings from BOTH table content AND column titles
3. Process complete tables including continuations across pages
4. If you find only one investment value for the security, use it for both the investment_beginning_value and investment_ending_value.
{% if plan_descriptor %}
5. If present use the descriptor of the plan sponsor account to crearly identify the corresponding holding tables to the entity.
{% elif ime_descriptor %}
5. If present use the descriptor of the investment management account to crearly identify the corresponding holding tables to the entity.
{% endif %}

TABLE IDENTIFICATION:
1. Process tables with these titles (case-insensitive):
- "EQUITIES"
- "SECURITIES"
- "ASSETS"
- "HOLDINGS"
- Any variations (e.g., "EQUITIES (continued)")
2. Look for holdings in:
- Main table content
- Description columns
- Related footnotes
3. EXCLUDE:
- Summary sections
- Aggregate totals
- Projected values
- Holdings for other entities
- Tables where the title has "Activities" in it. (case-insensitive)

#### OUTPUT MARKDOWN
Generate a mardown for the tables with the corresponding id. Do not include any other information from the document.

# Investment management entity name: {{ name }}
## Investment management entity account_type name: {{ ime_account_type }}
## Investment management entity id: {{ entity_id }}
## Investment management entity account id: {{ ime_account_id }}
## Plan sponsor entity name: {{ sponsor_name }}
## Plan sponsor entity account_type name: {{ plan_account }}
## Plan sponsor entity descriptor: {{ plan_descriptor }}
## Plan sponsor entity account_number: {{ plan_number }}
## Plan sponsor entity id: {{ sponsor_id }}
## Plan sponsor entity account id: {{ pse_account_id }}

FOR ALL OF THE TABLES CORRESPONDING TO THE ENTITY
#### Table ID: {{ table_id }}
#### Page number: page numbers where the tables are found, use the page numbers from the original document
#### Table Information

#### IMPORTANT: 
- Do not include in the output: ```markdown at the beginning and ``` at the end of the markdown.
- Extract the information as it is. Do not modify it.