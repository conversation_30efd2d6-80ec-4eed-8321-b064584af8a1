Extract all transactional activities involving securities in the markdown document.

KEY CONCEPTS:
- Transactional activities include: buying, selling, transferring securities, receiving dividends
- Each transaction typically includes: date, type, security name, quantity, price, total amount, amount
- Focus ONLY on actual transactions, not holdings or portfolio summaries

CRITICAL RULES:
1. NO VALUES SHOULD BE INVENTED OR INFERRED - use only explicit information from the text.
2. Extract transactions from BOTH table content AND column titles if they represent distinct transactional information.
3. Process complete tables including continuations across pages.
4. **SPECIFIC INSTRUCTION FOR TABLES WITH SECURITIES IN COLUMNS AND ACTIVITIES IN ROWS:**
When a table is structured with:
- **Securities/Funds as Column Headers** (e.g., 'Russell Real Assets', 'BTC US Equity Index').
- **Financial Activities/Events as Row Labels** (e.g., 'Permanente Contributions', 'Exchanges', 'Change in Account Value').
You MUST generate a **separate transaction object for each relevant activity row, for each security column.**
- The `name` of the security for each transaction will be the **column header**.
- The `transaction_type` for each transaction will be the **row label** (e.g., 'Permanente Contributions', 'Exchanges', 'Change in Account Value').
- The `investment_value` for that specific transaction will be the numerical value found in the **cell at the intersection** of that security's column and that activity's row.
- **EXCLUDE rows** explicitly labeled 'Beginning Balance' or 'Ending Balance' from being treated as their own `transaction_type`. These rows describe states, not the activities themselves. However, 'Change in Account Value' IS a valid `transaction_type`.

TRANSACTION IDENTIFICATION:
1. Look for sections with:
- Individual transaction dates
- Type: security type (e.g. Equity Closed Fund or CLF) usually near the name of the transactions.
- Transaction types (buy, sell, bought, sold, purchased, dividend, reinvestment, interest, transfer in, transfer out, fees, redemption, stock split, corporate action, employee deferral, loan, exchanges, contributions, etc.)
- Per-entry quantities and prices
- Check the name of the table, could also be a transaction type (Divident pyments, etc.)
- Transactions can have an inicial date and end date
- Transactions amount can have a small value amount or be also cero.
- Transactions can also be negative amounts.
2. EXCLUDE:
- Summary sections
- Static holdings information
- Transactions for other entities
- Tables where the title has "Holdings" in it (case-insensitive)

CONDITION
IF the transactions are on the column of a table, extract each row as a transaction, look for the transaction types on each row for every transaction.

MANDATORY FIELDS can not be null:
- tt_id: Extract the current table id from the markdown. Do not infer it or invent it.

OPTIONAL FIELDS (use null if missing):
- name: Security name with the surrounding information. If not found do not add the transaction. DO NOT use the ticker as NAME.
- transaction_date: Transaction date, take the newest date if there are more than one. Only take the date correspondings to the transaction in that row or column.
- ticker: unique series of letters (and sometimes numbers) assigned to a publicly traded security for trading and identification purposes. Usually inside the same table the transaction is being extracted (only if explicit)
- transaction_type: (buy, sell, bought, sold, purchased, dividend, reinvestment, interest, transfer in, transfer out, fees, redemption, stock split, corporate action, employee deferral, loan, exchanges, contributions, etc.)
- type: security type (e.g. Equity Closed Fund or CLF) usually near the name of the transactions.
- exchange: Trading marketplace (only if explicit)
- currency: ISO 4217 code (if explicit or clearly inferable)
- investment_value: End of period value as string (exactly as in text). Do not invent it.
- class: Must be one of this values: ["Shares", "Debt", "Funds", "Government", "InsPenFunds", "Open-End Fund", "Other"]
- security_unique_id: can be the 3 of the following values:
    - isin: 12-character alphanumeric identifier
    - cusip: 9-character alphanumeric identifier
    - other: First found identifier from: ["AMFI", "SEDOL", "LipperID", "CINS", "VALOR", "APIR", "WKN", "CIN", "BD", "BLC", "CHCCODEFE", "CHNCODEBE", "CHNCODEFE", "COMMONCODE", "FUNDSERV", "IDNSECCODE", "ISMA", "ITA", "JASDA", "KOFIA", "SICC", "SICOVAM", "SVM", "THASECCODE", "TWNCODE", "WERTPAPIER"]
- ime_id: Extract the investment management entity id from the markdown. Do not infer it or invent it. Can be null.
- ps_id: Extract the plan sponsor entity id from the markdown. Do not infer it or invent it. Can be null.
- imeacc_id: Extract the investment management entity account id from the markdown. Do not infer it or invent it. Can be null.
- psacc_id: Extract the plan sponsor entity account id from the markdown. Do not infer it or invent it. Can be null.
- page_number: Extract the page number where the transaction is located. Use the table page num as value.

#### OUTPUT JSON FORMAT:
Must be valid JSON matching this structure exactly:
{
    "transaction": [
        {
            "name": "Security Name",
            "ticker": "SYMBOL",
            "class": "ValidClass",
            "transaction_date": "TransactionDate",
            "type: "TypeDescriptor"
            "transaction_type": "TransactionType",
            "exchange": "Market",
            "currency": "ISO",
            "investment_value": "Value",
            "security_unique_id": "12CharCode",
            "tt_id": "table_id",
            "ime_id": "investment_management_entity_id",
            "ps_id": "plan_sponsor_entity_id"
            "imeacc_id": "ime_account_id",
            "psacc_id": "pse_account_id",
            "page_number": 1
        }
    ]
}

#### INPUT MARKDOWN DOCUMENT
{{chunk}}