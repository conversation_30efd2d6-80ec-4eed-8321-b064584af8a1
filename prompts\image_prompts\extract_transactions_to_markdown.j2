{# prompts/extract_transactions_to_markdown.j2 #}
Extract all transactional activities involving securities. Do not include de holdings and summary tables.

Use the following information to extract the transactions:
- investment entity name: {{ name }}
- investment entity id: {{ entity_id }}
- investment entity account_type name: {{ ime_account_type }}
- investment entity account id: {{ ime_account_id }}
- investment entity account descriptor: {{ ime_account_descriptor }}
- plan sponsor entity name: {{ sponsor_name }}
- plan sponsor entity id: {{ sponsor_id }}
- plan sponsor entity account_type name: {{ plan_account }}
- plan sponsor entity account_number: {{ plan_number }}
- plan sponsor entity account id: {{ pse_account_id }}
- plan sponsor entity descriptor: {{ pse_account_descriptor }}

{# Solo se extraen transacciones si hay un tipo de cuenta de plan #}
{% if plan_account %}
Only Extract the transactions correspondings to the plan sponsor entity type: {{ plan_account }}
{% endif %}

KEY CONCEPTS:
- Transactional activities include: buying, selling, transferring securities, receiving dividends
- Each transaction typically includes: date, type, security name, quantity, price, total amount, amount
- Identify the transactions tables (tables with the activity information in the document) and asign an id to each of them (e.g. "1", "2"): {{ table_id }} + (The id given)

CRITICAL RULES:
1. NO VALUES SHOULD BE INVENTED OR INFERRED - use only explicit information from the text.
2. Process complete tables including continuations across pages.
3. Do not modify the information from the tables. DO NOT CHANGE AMOUNT VALUES.
4. Also add if the transaction amount is negative using "-" before the amount.
{# Lógica condicional que antes estaba en Python, ahora está en la plantilla #}
{% if ime_account_descriptor %}
5. If the investment management account descriptor is available use it to identify the corresponding transaction tables to the entity.
{% elif pse_account_descriptor %}
5. If the pse account descriptor is available use it to identify the corresponding transaction tables to the entity.
{% endif %}

EXCLUDE:
- Summary sections
- Static holdings information
- Transactions for other entities
- Tables where the title has "Holdings" in the title (case-insensitive)

#### OUTPUT MARKDOWN
Generate a mardown for the tables with the corresponding id. Do not include any other information from the document.
# Investment management entity name: {{ name }}
## Investment management entity account_type name: {{ ime_account_type }}
## Investment management entity id: {{ entity_id }}
## Investment management entity account id: {{ ime_account_id }}
## Plan sponsor entity name: {{ sponsor_name }}
## Plan sponsor entity account_type name: {{ plan_account }}
## Plan sponsor entity account_number: {{ plan_number }}
## Plan sponsor entity id: {{ sponsor_id }}
## Plan sponsor entity account id: {{ pse_account_id }}

FOR ALL OF THE TABLES CORRESPONDING TO THE ENTITY
    #### Table ID: {{ table_id }}
    #### Page number: page numbers where the tables are found, use the page numbers from the original document
    #### Table Information (Do not modify this information leave it at it is)

#### IMPORTANT: 
- Do not include in the output: ```markdown at the beginning and ``` at the end of the markdown.
- Extract the information as it is. Do not modify it.