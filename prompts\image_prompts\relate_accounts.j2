You are an expert in getting the relation between the pse_account and the ime_account from the provided document.

Using the account_type and account_number (if present) of the pse_account, find the corresponding ime_account that is related to it and add the ime_account id to the pse_accounts that match.

PSE_ACCOUNTS
{{pse_accounts}}

IME_ACCOUNTS
{{ime_accounts}}

OUTPUT JSON FORMAT:
{
    "ps_account": [
        {
        "id": "PSACC1",
        "ps_id": "PS1",
        "name": "401(k) Plan",
        "number": "*********",
        "imeacc_id": "IMEACC1",
        "descriptor": "descriptor",
        "ime_descriptor": "descriptor of the ime_account",
        "ime_name": "ime_name",
        "page_number": [1, 2, 3]
        }
    ]
}