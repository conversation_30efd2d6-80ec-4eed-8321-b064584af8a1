Given the following finacial document. Check if any duplicate investment management entities are present. If so, remove them and return the unique ones.
- Only mantain the one of the entities that refer to the same institution:
    * Treat variations of the same entity name as a single entity.
    * Use the most complete/formal version of the entity name when multiple variations exist.
    * Check for abbreviations, shortened names, and legal variations of the same institution.
    * Do not create separate entities for different formatting or capitalization of the same name.
    * Do not modify the original information from the json input.

# Current entities
{{investment_entities}}

OUTPUT JSON FORMAT:
{
"investment_management_entity": [
    {
        "name": "entity_name",
        "address": "address_entity",
        "website": "webpage_entity",
        "entity_unique_id": "entity_unique_id",
        "page_number": page_num_entity,
        "is_placeholder": "is_placeholder"
    }
]
}