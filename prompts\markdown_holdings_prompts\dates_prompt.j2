####
INVESTMENT_MANAGEMENT_ENTITY_NAME: {{ ime_name }}
INVESTMENT_MANAGEMENT_ENTITY_ID: {{ ime_id }}
####

Instructions:
 - Extract the exact start and end dates that define the {{ ime_name }} financial statement reporting period.

Validation Rules:
    1. Dates must be from the same reporting period
    2. Format must match source document exactly
    3. Must be actual dates (not descriptions)
    4. Must include both start and end dates when available, if not mantain the end date for the start date.
    5. Single "as of" dates should be treated as end dates
    6. It should be only one start and end date. Do not add multiple dates in the response.
    
Expected Output Format:
The output must be a valid JSON object following this exact structure, with no additional text or comments:

{
"date": [
        {
            "ime_id": {{ ime_id }},
            "ime_name": {{ ime_name }},
            "data":{
                "start_date": {
                    "value": "01/01/2025",
                    "page_number": 1
                },
                "end_date": {
                    "value": "01/01/2025",
                    "page_number": 1
                    ]
                }
            }
        }
    ]
}
INPUT:
{{ statement_text }}