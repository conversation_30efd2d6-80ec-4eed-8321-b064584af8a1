You are an automated data extraction engine. Your task is to identify and extract holdings tables from the provided text chunk that are relevant to the specified entities and accounts.

ENTITIES AND ACCOUNTS OF INTEREST:
{{ entities_and_accounts }}

**CRITICAL INSTRUCTIONS:**
1.  **IDENTIFY:** Scan the chunk for tables under headers like "HOLDINGS", "INVESTMENTS", "SCHEDULE OF INVESTMENTS", "ASSETS", "SECURITIES", "EQUITIES".
2.  **VALIDATE:** For each potential table, verify that the context links it to one of the `ENTITIES AND ACCOUNTS OF INTEREST`. If there is no clear link, IGNORE the table.
3.  **EXTRACT:** Convert the raw text of each validated table into a structured list of rows. Do not modify, clean, or interpret the data yet. Preserve the exact text.
4.  **EXCLUDE:** Do NOT include summary rows, totals, subtotals, or headers in the extracted `rows` data. Only extract rows that represent an individual security.
5.  **OUTPUT:** Respond ONLY with a valid JSON object. If no relevant holdings tables are found, return `{"holdings_tables": []}`.

**OUTPUT JSON FORMAT:**
{
    "holdings_tables": [
        {
            "page_number": <integer>,
            "table_id": "HT001",
            "table_title": "<The exact title of the table>",
            "headers": ["<header1>", "<header2>", ...],
            "rows": [
                ["<cell1_text>", "<cell2_text>", ...],
                ["<cell1_text>", "<cell2_text>", ...]
            ]
        }
    ]
}

**EXAMPLE:**
If the text contains:
"### Page 5
**Schedule of Investments**
Description | Shares | Market Value
Vanguard 500 | 1,000 | $400,000
Apple Inc.   | 500   | $75,000"

Your output should be:
{
    "holdings_tables": [
        {
            "page_number": 5,
            "table_id": "HT001",
            "table_title": "Schedule of Investments",
            "headers": ["Description", "Shares", "Market Value"],
            "rows": [
                ["Vanguard 500", "1,000", "$400,000"],
                ["Apple Inc.", "500", "$75,000"]
            ]
        }
    ]
}