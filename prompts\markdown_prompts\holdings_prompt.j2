Extract all holdings securities for the following context from the provided text chunks.

CONTEXT:
- Investment Management Entity (ime): {{ ime_id }} - {{ ime_name }}
- Account (ime_acc): {{ ime_acc_id }} - {{ ime_acc_name }}
- Plan Sponsor (ps): {{ ps_id }} - {{ ps_name }}
- Plan (ps_plan): {{ ps_plan_id }} - {{ ps_plan_name }}
- Entity details: {{ entity_details }}

KEY CONCEPTS:
- Holdings represent current securities/assets/equities owned at a specific point in time
- Each holding must have complete identification and value information.
- Focus ONLY on actual holdings, not transactions or summaries
- The holdings can be in different languages.
- Identify the holdings tables (tables with the security information in the document) and asign an id to each of them (e.g. "1", "2"): {table_id} + (The id given)
- **ht_id**: This is a unique identifier for each holdings type table. Assign "HT001" to the first holdings table, "HT002" to the second, and so on. Use this to group holdings that come from the same table.

CRITICAL RULES:
1. NO VALUES SHOULD BE INVENTED OR INFERRED - use only explicit information from text
2. Extract holdings from BOTH table content AND column titles
3. Process complete tables including continuations across pages
4. If you find only one investment value for the security, use it for both the beginning_value and ending_value.
5. Remove duplicate holdings from overlapping chunks
6. The beginning_value and ending_value usually are in the same page number.

TABLE IDENTIFICATION:
1. Process tables with these titles (case-insensitive):
   - "EQUITIES"
   - "SECURITIES"
   - "ASSETS"
   - "HOLDINGS"
   - Any variations (e.g., "EQUITIES (continued)")
2. Look for holdings in:
   - Main table content
   - Description columns
   - Related footnotes
3. EXCLUDE:
   - Summary sections
   - Aggregate totals
   - Projected values
   - Holdings for other entities, accounts, plans, or plan sponsors

MANDATORY FIELDS can not be null:
- name: Security name only do not include class/type descriptors like "Mid-Cap", "Large-Cap". If not found do not add the holding.
- class: Must be one of this values: ["Shares", "Debt", "Funds", "Government", "InsPenFunds" (Insurance or Pension Funds), "Open-End Fund", "Other"]
- page_number: Number only (e.g., 2, 1, 5). Do not infer or invent.

OPTIONAL FIELDS (use null if missing):
- ticker: Trading symbol (only if explicit) it can be found within parenthesis. E.g. (ticker, VG500)
- security_unique_id: Unique identifier for the security (if explicit, e.g., CUSIP, ISIN)
- exchange: Trading marketplace (only if explicit)
- currency: ISO 4217 code (if explicit or clearly inferable)
- type: Type of security
- beginning_value: Start value as string (exactly as in text)
- ending_value: End value as string (exactly as in text)
- security_unique_id: can be the 3 of the following values:
            - isin: 12-character alphanumeric identifier
            - cusip: 9-character alphanumeric identifier
            - other: First found identifier from: ["AMFI", "SEDOL", "LipperID", "CINS", "VALOR", "APIR", "WKN", "CIN", "BD", "BLC", "CHCCODEFE", "CHNCODEBE", "CHNCODEFE", "COMMONCODE", "FUNDSERV", "IDNSECCODE", "ISMA", "ITA", "JASDA", "KOFIA", "SICC", "SICOVAM", "SVM", "THASECCODE", "TWNCODE", "WERTPAPIER"]

OUTPUT FORMAT:
Must be valid JSON matching this structure exactly:
{
    "holdings": [
        {
            "ime_id": {{ ime_id }},
            "ime_acc_id": {{ ime_acc_id }},
            "ps_id": {{ ps_id }},
            "ps_plan_id": {{ ps_plan_id }},
            "ht_id": "HT001",
            "data": {
                "name": {
                    "value": "Vanguard SP500",
                    "page_number": 1
                },
                "ticker": {
                    "value": "VG500",
                    "page_number": 1
                },
                "class": {
                    "value": "Shares",
                    "page_number": 1
                },
                "type": {
                    "value": "ETF",
                    "page_number": 1
                },
                "exchange": {
                    "value": null,
                    "page_number": 1
                },
                "currency": {
                    "value": null,
                    "page_number": 1
                },
                "beginning_value": {
                    "value": "$123.456",
                    "page_number": 1
                },
                "ending_value": {
                    "value": "$123.456",
                    "page_number": 1
                },
                "security_unique_id": {
                    "value": "123456789",
                    "page_number": 1
                    ]
                }
            }
        }
    ]
}

## CHUNKS TEXT:
{{ improved_texts_full }}