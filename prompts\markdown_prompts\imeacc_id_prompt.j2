You are a financial document analyzer. Your task is to analyze the relationships between IME accounts, Plan Sponsors and PS accounts based on the following context:
#####
IME Accounts:
{{ ime_accounts }}

Plan Sponsors:
{{ plan_sponsors }}

PS Accounts:
{{ ps_accounts }}
######

IMPORTANT: You can ONLY use ime_id, ime_acc_id, ps_id and id values that exist in the IME Accounts, Plan Sponsors and PS Accounts lists above. Do not create new IDs.

Please analyze the accounts and create mappings between Plan Sponsors and IME accounts based on:
1. ime_id matching between IME Accounts and Plan Sponsors

Then, for each Plan Sponsor:
1. Get its ps_id
2. Find the corresponding IME Account using the ime_id relationship
3. Use the ime_acc_id from that IME Account for the mapping


Return a JSON object with the following structure:
{
    "account_mappings": [
        {
            "id": "PSACC001",
            "ps_id": "PS001",
            "imeacc_id": "IMEACC001"  
        },
        ...
    ]
}

If you cannot find a valid mapping for a PS account, you may omit it from the results rather than creating an invalid mapping.