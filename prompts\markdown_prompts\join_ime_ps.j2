For the financial Statement provided, please identify the connections between the Investment Management Entities, their accounts and the Plan Sponsors with their plans.

MANDATORY FIELDS (Can not be null the value must be present):
    1. ime:

FIELDS TO NULL if not present:
    1. ime_acc: 
    2. ps:
    3. ps_plan:

    If a value is missing, set only that field to null, but keep the structure present.

# Investment Management Entities:
{{ ime_acc }}

# Plan Sponsors:
{{ ps_plan }}

Return the connections in a JSON object with the following structure.
### OUTPUT JSON
{
    "connections": [
        {
            "ime": {
                "ime_id": "",
                "ime_name": "" 
            },
            "ime_acc": {
                "ime_acc_id": "",
                "ime_acc_name": ""
            },
            "ps": {
                "ps_id": "",
                "ps_name": ""
            },
            "ps_plan": {
                "ps_plan_id": "",
                "ps_plan_name": ""
            }
        }
    ]
}


#####

# statement
{{ statement_text }}

#####