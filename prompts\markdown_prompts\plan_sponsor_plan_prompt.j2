Your task is to extract the Name and Number of the Plan Sponsor Plans from the following Investment Management Entity and Plan Sponsor.

{{ entity_details }}

In the context of employment-related retirement plans, a plan refers to the specific category or structure of a retirement savings account that is established for employees to save for their retirement. Each plan has its own rules, contribution limits, tax implications, and withdrawal options.
Here is a list of common retirement and benefit plan types offered by plan sponsors. Use this list as a primary reference. However, be aware that the document might use slightly different phrasing or mention plan types not explicitly listed here. If a plan is clearly sponsored by the specified Plan Sponsor Entity and has a discernible type, extract it.

**401(k) Plan:** Employer-sponsored retirement savings plan allowing pre-tax employee contributions, often with employer match. Tax-deferred growth.
**403(b) Plan:** Similar to 401(k), for employees of public schools and certain tax-exempt organizations. Pre-tax contributions, tax-deferred growth.
**457(b) Plan:** Non-qualified, tax-advantaged retirement plan for state/local government and certain non-profit employees. Pre-tax contributions, tax-deferred growth. No early withdrawal penalties for pre-retirement distributions.
**Defined Contribution Plan:** Retirement plan where contributions are made by employer, employee, or both. Benefit depends on contributions and investment performance (e.g., 401(k), 403(b)).
**Defined Benefit Plan (Pension Plan):** Employer promises a specified monthly benefit upon retirement based on salary/service. Employer funds and manages investments.
**Profit-Sharing Plan:** Employer contributes a portion of company profits to employees' retirement accounts. Contributions can vary.
**Employee Stock Ownership Plan (ESOP):** Retirement plan providing employees with ownership interest in the company via stock.
**SIMPLE IRA (Savings Incentive Match Plan for Employees):** For small businesses (<100 employees). Allows employee/employer contributions, employer match required. Tax-deductible contributions, tax-deferred growth.
**Thrift Savings Plan (TSP):** Retirement savings plan for federal employees and uniformed services members. Pre-tax contributions, government matching.
**State or Teachers Retirement System Pension:** Defined benefit plan for state or educational employees, providing guaranteed income based on salary/service.
**TIAA Annuity Contract:** Investment product for retirement savings, often for educators/state employees, offering fixed or variable returns for retirement income.

**IMPORTANT:**  
If the Plan Sponsor is not present or not explicitly mentioned, you must still search for and extract all possible plan sponsor plans using the context provided in entity_details and the document. Do not skip plan extraction due to missing plan sponsor information.

Be careful when extracting a plan, you cannot mistake a plan for an Investment Management Entity or any Account that belongs to the Investment Management Entity so, DON'T INCLUDE POSSIBLE INVESTMENT MANAGEMENT ENTITY ACCOUNTS, otherwise you will be penalized.

MANDATORY FIELDS (Can not be null the value must be present):
    1. ps_plan_name:
        - Official plan name only
        - Exclude descriptive text and marketing taglines
        - If no plan is found, it could be in the title of the document.

    2. page_number:
        - Number only (e.g., 2, 1, 5). Do not infer or invent.

FIELDS TO NULL if not present:
    1. ps_plan_number:
        - It is the plan number.
        - It has to be a valid number. Usually near to the name of the plan.

    2. descriptor: Construct this as a combination of relevant descriptors such as the account category (e.g employer sponsored plans, basic plans and special plans) and any specific features (e.g. government sponsored, stock ownership and teachers/educational workers plans). If not present, do not add it.


    if any value is null put all the inside structure to null:
        {
            "value": null,
            "page_number": null
        }

Return a json object with the following structure.

### OUTPUT JSON
{
    "ps_accounts": [
        {
            "ps_id": {{ ps_id }},
            "descriptor": "employer sponsored plans",
            "data": {
                "ps_plan_name": {
                    "value": "401(k) Plan",
                    "page_number": 1
                },
                "number": {
                    "value": "123-456",
                    "page_number": 1
                }
            }
        },
        {
            "ps_id": {{ ps_id }},
            "descriptor": "special plans",
            "data": {
                "ps_plan_name": {
                    "value": "403(b) Plan",
                    "page_number": 1
                },
                "number": {
                    "value": "789-012",
                    "page_number": 1
                }
            }
        }
    ]
}

INPUT:
{{ statement_text }}


