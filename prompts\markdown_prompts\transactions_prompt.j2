Extract all transactional activities involving securities for the following context. Do not include holdings or summary tables.

CONTEXT:
- Investment Management Entity (ime): {{ ime_id }} - {{ ime_name }}
- Account (ime_acc): {{ ime_acc_id }} - {{ ime_acc_name }}
- Plan Sponsor (ps): {{ ps_id }} - {{ ps_name }}
- Plan (ps_plan): {{ ps_plan_id }} - {{ ps_plan_name }}
- Entity details: {{ entity_details }}

Only Extract the transactions correspondings to the plan sponsor entity type: {{ ps_name }}

KEY CONCEPTS:
- Transactional activities include: buying, selling, transferring securities, receiving dividends
- Each transaction typically includes: date, type, security name, quantity, price, total amount, amount
- Each single transaction data must be in the same table and page_number.
- Focus ONLY on actual transactions, not holdings or portfolio summaries
- Only extract transactions belonging to the specific account and plan above. Do not add transactions from other accounts, plans, or entities.
- Do not extract separate transactions for each cell in the same row
- Identify the transactions tables (tables with the activity information in the document) and assign an id to each of them (e.g. "1", "2"): {table_id} + (The id given)
- **tt_id**: This is a unique identifier for each transaction type table. Assign "TT001" to the first transaction table, "TT002" to the second, and so on. Use this to group transactions that come from the same table.
- **security_unique_id**: This field should contain the unique identifier for the security involved in the transaction. It can be an ISIN (12-character), CUSIP (9-character), or another recognized identifier (see list below). Use the first identifier found in the document for the security.

CRITICAL RULES:
1. NO VALUES SHOULD BE INVENTED OR INFERRED - use only explicit information from the text.
2. For the investment value look for the ending balance or last value for the transaction.
3. Extract transactions from BOTH table content AND column titles.
4. Process complete tables including continuations across pages.
5. The investment_value is in the same table and page number.

TRANSACTION IDENTIFICATION:
1. Look for sections with:
   - Individual transaction dates
   - Type (descriptor of the transaction)
   - Transaction types (buy, sell, bought, sold, purchased, dividend, reinvestment, interest, transfer in, transfer out, fees, redemption, stock split, corporate action, etc.)
   - Per-entry quantities and prices
   - Table name may also be a transaction type (e.g., Dividend payments)
   - Transactions can have an initial date and final date
   - Transactions amount can be small, zero, or negative
2. EXCLUDE:
   - Summary sections
   - Static holdings information
   - Transactions for other entities, accounts, plans, or plan sponsors
   - Tables where the title has "Holdings" in the title (case-insensitive)

SPECIAL TABLE RULE:
- If you find a table where the first column is "Activity" and the other columns are security names, treat each row (except for "Beginning Balance", "Ending Balance", and totals) as a transaction.
- The value in the "Activity" column is the transaction type (use it for "transaction_type" fields).
- For each security column, if the cell is not empty or zero, create a transaction with the corresponding security name and the value as "investment_value".
- Ignore rows like "Beginning Balance", "Ending Balance", "Total", or any summary rows.

MANDATORY FIELDS can not be null:
- name: Security name only (exclude class/type). If not found do not add the transaction. DO NOT use the ticker as NAME.
- type: This can be either buy, sell, bought, sold, purchased, dividend, reinvestment, interest, transfer in, transfer out, fees, redemption, stock split, corporate action, etc]
- page_number: Number only (e.g., 2, 1, 5). Do not infer or invent, all fields for a single transaction must have the same page_number.

OPTIONAL FIELDS (use null if missing but at least one of them must be present):
- date: Transaction date, take the newest date if there are more than one. Only take the date correspondings to the transaction in that row or column.
- ticker: Trading symbol (only if explicit)
- exchange: Trading marketplace (only if explicit)
- currency: ISO 4217 code (if explicit or clearly inferable)
- Investment value: End of period value as string (exactly as in text)
- class: Must be one of this values: ["Shares", "Debt", "Funds", "Government", "InsPenFunds", "Open-End Fund", "Other"]
- security_unique_id: can be the 3 of the following values:
        - isin: 12-character alphanumeric identifier
        - cusip: 9-character alphanumeric identifier
        - other: First found identifier from: ["AMFI", "SEDOL", "LipperID", "CINS", "VALOR", "APIR", "WKN", "CIN", "BD", "BLC", "CHCCODEFE", "CHNCODEBE", "CHNCODEFE", "COMMONCODE", "FUNDSERV", "IDNSECCODE", "ISMA", "ITA", "JASDA", "KOFIA", "SICC", "SICOVAM", "SVM", "THASECCODE", "TWNCODE", "WERTPAPIER"]

OUTPUT FORMAT:
Return a valid JSON with this structure:
{
    "transactions": [
        {
            "ime_id": "{{ ime_id }}",
            "ime_acc_id": "{{ ime_acc_id }}",
            "ps_id": "{{ ps_id }}",
            "ps_acc_id": "{{ ps_plan_id }}",
            "tt_id": "TT001",
            "data": {
                "name": {
                    "value": "Vanguard SP500",
                    "page_number": 1
                },
                "transaction_date": {
                    "value": "01/24",
                    "page_number": 1
                },
                "transaction_type": {
                    "value": "01/24",
                    "page_number": 1
                },
                "ticker": {
                    "value": "VG500",
                    "page_number": 1
                },
                "class": {
                    "value": "Shares",
                    "page_number": 1
                },
                "type": {
                    "value": "ETF",
                    "page_number": 1
                },
                "exchange": {
                    "value": null,
                    "page_number": 1
                },
                "currency": {
                    "value": null,
                    "page_number": 1
                },
                "investment_value": {
                    "value": "$123.456",
                    "page_number": 1
                },
                "security_unique_id": {
                    "value": "123456789",
                    "page_number": 1
        }
    ]
}

## CHUNKS TEXT:
{{ improved_texts_full }}