You are a reasoning engine that connects financial data. Your task is to determine the owner of the provided `holding_section` by matching it to the most appropriate entity from the `entities_list`.

CONTEXT:
- The holding section was found on page {{ page_number }}.
- Here is the full text of page {{ page_number }} for context:
  ---
  {{ page_text }}
  ---

HOLDING SECTION TO ANALYZE:
---
{{ section_text }}
---

LIST OF POTENTIAL OWNERS IN THE DOCUMENT:
---
{{ entities_list_json }}
---

INSTRUCTIONS:
1.  Analyze the `page_text` (headers, footers, titles) and the `holding_section` itself.
2.  Find the entity from the `entities_list` that is the clear owner of these holdings.
3.  Pay close attention to account numbers, plan names, and sponsor names mentioned near the holding section.
4.  Return the complete set of IDs (ime_id, ime_acc_id, ps_id, ps_plan_id) for the identified owner.
5.  If a level of the hierarchy is not applicable (e.g., it's a personal brokerage account with no Plan Sponsor), its ID should be null.
6.  If you cannot determine the owner with high confidence, return null for all IDs.

Expected Output Format:
The output must be a valid JSON object following this exact structure:
{
    "owner": {
        "ime_id": "IME001",
        "ime_acc_id": "IMEACC001",
        "ps_id": null,
        "ps_plan_id": null
    }
}