Extract all holdings securities from the provided table text. The holdings belong to the entity specified in the CONTEXT.

CONTEXT (Owner of these holdings):
{{ owner_context }}

CRITICAL RULES:
1.  NO VALUES SHOULD BE INVENTED OR INFERRED. Use only explicit information from the text.
2.  Extract every row from the table that represents a distinct security.
3.  If you find only one investment value for the security, use it for both the beginning_value and ending_value.
4.  The page number for all extracted data is {{ page_number }}.
5.  Assign a unique table identifier (`ht_id`) for each distinct table you process (e.g., "HT001", "HT002").

MANDATORY FIELDS (cannot be null):
- name: Security name.
- class: Must be one of: ["Shares", "Debt", "Funds", "Government", "InsPenFunds", "Open-End Fund", "Other"].
- page_number: The provided page number.

OPTIONAL FIELDS (use null if missing):
- ticker, security_unique_id, exchange, currency, type, beginning_value, ending_value.

OUTPUT FORMAT:
Must be a valid JSON matching this structure exactly:
{
    "holdings": [
        {
            "ht_id": "HT001",
            "data": {
                "name": { "value": "Vanguard SP500", "page_number": {{ page_number }} },
                "ticker": { "value": "VOO", "page_number": {{ page_number }} },
                "class": { "value": "Funds", "page_number": {{ page_number }} },
                "type": { "value": "ETF", "page_number": {{ page_number }} },
                "exchange": { "value": null, "page_number": {{ page_number }} },
                "currency": { "value": "USD", "page_number": {{ page_number }} },
                "beginning_value": { "value": "$123,456", "page_number": {{ page_number }} },
                "ending_value": { "value": "$123,456", "page_number": {{ page_number }} },
                "security_unique_id": { "value": null, "page_number": {{ page_number }} }
            }
        }
    ]
}

HOLDINGS TABLE TEXT:
{{ holdings_table_text }}