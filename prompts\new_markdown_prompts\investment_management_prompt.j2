Extract investment management entities from financial statements according to these specifications:

KEY CONCEPTS:
- Investment management entities are financial institutions managing investment accounts.
- Each entity must have complete identification.
- NO INFORMATION SHOULD BE INVENTED - use only explicit data from text.

ENTITY IDENTIFICATION:
    1. Valid Entities:
    - Financial institutions (e.g., banks, brokerages)
    - Insurance companies
    - Investment management firms
    - DO NOT INCLUDE: "Jonas Wealth Management" or similar advisory firms

    2. Entity Location in Document:
    - Headers and footers
    - Account summary sections
    - Statement overview pages
    - Legal disclaimers

MANDATORY FIELDS (Can not be null the value must be present):

    1. entity_name:
    - Official institution name only
    - Exclude descriptive text and marketing taglines
    - Must be a recognized financial institution
    - If no entity is found, it could be in the title of the document.

    2. page_number:
    - Number only (e.g., 2, 1, 5). Do not infer or invent.

FIELDS TO NULL if not present:

    1. website:
        - It has to be a valid URL. Usually similar to the name of the entity.

    2. address:
    - Do not use the customer address, only the physical location of the entity
    - Exclude phone numbers and emails
    - Format as shown in document

    3. entity_unique_id: can be the 2 of the following values:
        - DUNS:
            - 9 Digit id with the Data Universal Numbering System managed by Dun & Bradstreet (D&B)
        - TaxID:
            - A Taxpayer Identification Number (TIN) is an identification number used by the Internal Revenue Service (IRS) in the administration of tax laws. 

    if any value is null put all the inside structure to null:
        {
            "value": null,
            "page_number": null
        }

Expected Output Format:
The output must be a valid JSON object following this exact structure, with no additional text or comments:
{
    "investment_management_entities": [
        {
            "data": {
                "ime_name": {
                    "value": "Fidelity Investments",
                    "page_number": 1
                },
                "address": {
                    "value": "900 Salem Street, Smithfield, RI 02917",
                    "page_number": 1
                },
                "website": {
                    "value": "www.fidelity.com",
                    "page_number": 1
                },
                "entity_unique_id": {
                    "value": "*********",
                    "page_number": 1
                }
            }
        }
    ]
}

INPUT:
{{ statement_text }}
