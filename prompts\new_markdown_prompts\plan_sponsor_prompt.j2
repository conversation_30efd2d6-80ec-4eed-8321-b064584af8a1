Instruction: 
- Extract all the plan sponsors and map them to the schema tool provided and their account types from the provided financial statement.

Description: 
- An organization or individual that initiates and manages an investment project or fund. This entity is responsible for raising capital, making investment decisions, and overseeing the execution of the investment strategy. Sponsors can be private equity firms, real estate developers, or other financial institutions

Given the entity_details extract the plan sponsor entities for this specific account.

entity_details: 

######

{{entity_details}}

######

The Investment Management Entity is not the same as the Plan Sponsor. Sometimes, there may be no Plan Sponsor

Use the following guidelines to identify and structure the data:

MANDATORY FIELDS (Can not be null the value must be present)
    1. ps_name:
        - Official Plan Sponsor Organization name
        - Must be distinct from investment manager
        - Usually found in plan documentation
        
    2. page_number:
        - Page number only (e.g., 2, 1, 5). Do not infer or invent.

FIELDS TO NULL if not present:
    1. entity_unique_id: can be the 2 of the following values:
        - DUNS:
            - 9 Digit id with the Data Universal Numbering System managed by Dun & Bradstreet (D&B)
        - TaxID:
            - A Taxpayer Identification Number (TIN) is an identification number used by the Internal Revenue Service (IRS) in the administration of tax laws.
    2. address:
        - Physical location only
        - Exclude contact information
        - Format as shown in document
    3. website:
        - Format as shown in document

    if any value is null put all the inside structure to null:
        {
                "value": null,
                "page_number": null
        }
        
CRITICAL RULES:
1. Never use investment managers as plan sponsors
2. Only include organizational entities (no individuals)
3. Do not add the Plan Sponsor if one of the MANDATORY FIELDS is null.
4. Verify plan types are retirement/benefit plans
5. Do not invent or infer missing information

Expected Output Format:
The output must be a valid JSON object following this exact structure, with no additional text or comments:

{
    "plan_sponsor_entities": [
        {
            "ime_id": {{ ime_id }},
            "data": {
                "ps_name": {
                    "value": "ABC Corporation",
                    "page_number": 1
                },
                "address": {
                    "value": "123 Business Street, City, State 12345",
                    "page_number": 1
                },
                "website": {
                    "value": "www.abccorp.com",
                    "page_number": 1
                },
                "entity_unique_id": {
                    "value": "123456789",
                    "page_number": 1
                }
            }
        }
    ]
}

**IMPORTANT** Do not make up any entities, If you don't find any of the values just use 'null'.

INPUT:
{{statement_text}}