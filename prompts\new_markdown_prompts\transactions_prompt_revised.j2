Extract all transactional activities from the provided table text. The transactions belong to the entity specified in the CONTEXT.

CONTEXT (Owner of these transactions):
{{ owner_context }}

CRITICAL RULES:
1.  NO VALUES SHOULD BE INVENTED OR INFERRED. Use only explicit information from the text.
2.  Extract every row from the table that represents a distinct transaction.
3.  The page number for all extracted data is {{ page_number }}.
4.  Assign a unique table identifier (`tt_id`) for each distinct table you process (e.g., "TT001", "TT002").

MANDATORY FIELDS (cannot be null):
- name: Security name.
- transaction_type: The type of activity (e.g., Buy, Sell, Dividend, Reinvestment).
- page_number: The provided page number.

OPTIONAL FIELDS (use null if missing):
- transaction_date, ticker, class, type, exchange, currency, investment_value, security_unique_id.

OUTPUT FORMAT:
Must be a valid JSON matching this structure exactly:
{
    "transactions": [
        {
            "tt_id": "TT001",
            "data": {
                "name": { "value": "Vanguard SP500", "page_number": {{ page_number }} },
                "transaction_date": { "value": "02/01/2024", "page_number": {{ page_number }} },
                "transaction_type": { "value": "Buy", "page_number": {{ page_number }} },
                "ticker": { "value": null, "page_number": {{ page_number }} },
                "class": { "value": "Funds", "page_number": {{ page_number }} },
                "type": { "value": "ETF", "page_number": {{ page_number }} },
                "exchange": { "value": null, "page_number": {{ page_number }} },
                "currency": { "value": "USD", "page_number": {{ page_number }} },
                "investment_value": { "value": "10000.00", "page_number": {{ page_number }} },
                "security_unique_id": { "value": null, "page_number": {{ page_number }} }
            }
        }
    ]
}

TRANSACTIONS TABLE TEXT:
{{ transactions_table_text }}