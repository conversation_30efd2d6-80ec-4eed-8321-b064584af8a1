import base64
import io
import os
from urllib.parse import urlparse
import fitz  # PyMuPDF
import requests
from PIL import Image
import aiohttp

from src.utils.logger import get_logger
LOGGER = get_logger("src.data_processing.document_preprocessor")

async def async_process_document_to_base64(file_url, dpi=150):
    """
    Fetches a document from a URL, determines its type from its content (MIME type), 
    and converts it to a list of base64 PNG strings.
    """
    LOGGER.info(f"Processing document from URL: {file_url}")
    base64_output_list = []

    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(file_url, timeout=60) as response:
                response.raise_for_status()
                input_document_bytes = await response.read()

        content_type_from_header = response.headers.get('Content-Type', '').lower().strip()
                
        # Lógica de fallback: si el encabezado no es fiable, usa la extensión del archivo
        if not content_type_from_header or content_type_from_header == 'application/octet-stream' or content_type_from_header.startswith('.'):
            LOGGER.warning(f"Unreliable Content-Type header: '{content_type_from_header}'. Falling back to file extension.")
            parsed_url = urlparse(file_url)
            _, file_extension = os.path.splitext(os.path.basename(parsed_url.path))
            effective_type = file_extension.lower()
        else:
            effective_type = content_type_from_header

        LOGGER.info(f"Processing with effective type: '{effective_type}'")

        if "pdf" in effective_type:
            base64_output_list = pdf_bytes_to_base64_pages_combined(input_document_bytes, dpi=dpi)
        elif "bmp" in effective_type:
            base64_output_list = bmp_bytes_to_base64_png_string(input_document_bytes)
        elif any(img_type in effective_type for img_type in ['png', 'jpeg', 'jpg', 'webp']):
            base64_image = base64.b64encode(input_document_bytes).decode("utf-8")
            base64_output_list.append(base64_image)
        else:
            LOGGER.error(f"Unsupported effective type: {effective_type}")
            return None

        if not base64_output_list:
            LOGGER.error(f"No content was successfully converted to base64 for {file_url}.")
            return None
        
        return base64_output_list

    except requests.exceptions.RequestException as e:
        LOGGER.error(f"Error fetching file from URL {file_url}: {e}")
        return None
    except Exception as e:
        LOGGER.error(f"An unexpected error occurred in document preprocessing: {e}", exc_info=True)
        return None
    

def process_document_to_base64(file_url, dpi=150):
    """
    Fetches a document from a URL, determines its type, and converts its content
    (pages for PDF/PPTX, image for BMP) to a list of base64 PNG strings.
    """
    LOGGER.info(f"Processing document from URL: {file_url}")
    base64_output_list = []

    try:
        # 1. Fetch the document content
        r = requests.get(file_url, timeout=60)
        r.raise_for_status()
        input_document_bytes = r.content

        # 2. Determine file type from URL (basic parsing)
        parsed_url = urlparse(file_url)
        file_name_from_path = os.path.basename(parsed_url.path)
        _, file_extension = os.path.splitext(file_name_from_path)
        file_extension = file_extension.lower()

        # 3. Process based on file type
        if file_extension == ".pdf":
            base64_output_list = pdf_bytes_to_base64_pages_combined(input_document_bytes, dpi=dpi)
        elif file_extension == ".bmp":
            base64_output_list = bmp_bytes_to_base64_png_string(input_document_bytes)
        elif file_extension in ['.png', '.jpg', '.jpeg', '.webp']:
            
            base64_image = base64.b64encode(input_document_bytes).decode("utf-8")
            base64_output_list.append(base64_image)
        else:
            LOGGER.error(f"Unsupported file extension: {file_extension}")
            return None

        if not base64_output_list:
            LOGGER.error(f"No content was successfully converted to base64 for {file_url}.")
            return None
        
        return base64_output_list

    except requests.exceptions.RequestException as e:
        LOGGER.error(f"Error fetching file from URL {file_url}: {e}")
        return None
    except Exception as e:
        LOGGER.error(f"Error details: {e}")
        return None
    
def pdf_bytes_to_base64_pages_combined(pdf_bytes, dpi=300, pages_per_image=2, background_color=(255, 255, 255)):
    """
    Converts PDF pages (from bytes) into larger, combined base64 encoded PNG images.
    Each combined image can contain up to 'pages_per_image' PDF pages stacked vertically.
    Returns a list of base64 encoded image strings.
    """
    combined_base64_images = []
    try:
        pdf_document = fitz.open(stream=pdf_bytes, filetype="pdf")
        num_total_pages = pdf_document.page_count

        for i in range(0, num_total_pages, pages_per_image):
            # Determine the actual pages for the current combined image
            # This handles the last chunk which might have fewer than pages_per_image
            page_indices_for_chunk = range(i, min(i + pages_per_image, num_total_pages))
            
            if not page_indices_for_chunk:
                continue

            pil_images_in_chunk = []
            current_chunk_max_width = 0
            current_chunk_total_height = 0

            # First pass for this chunk: render individual pages to PIL Images and get dimensions
            for page_number in page_indices_for_chunk:
                page = pdf_document.load_page(page_number)
                zoom = dpi / 72
                mat = fitz.Matrix(zoom, zoom)
                pix = page.get_pixmap(matrix=mat, alpha=False) # RGB

                img = Image.frombytes("RGB", [pix.width, pix.height], pix.samples)
                pil_images_in_chunk.append(img)
                
                current_chunk_max_width = max(current_chunk_max_width, img.width)
                current_chunk_total_height += img.height

            if not pil_images_in_chunk:
                continue

            # Create the combined image canvas
            # The canvas will have the max width of pages in the chunk and sum of their heights
            combined_image_pil = Image.new("RGB", (current_chunk_max_width, current_chunk_total_height), background_color)
            
            y_offset = 0
            for img in pil_images_in_chunk:
                # Calculate x-position to center the image if its width is less than max_width
                x_pos = (current_chunk_max_width - img.width) // 2
                combined_image_pil.paste(img, (x_pos, y_offset))
                y_offset += img.height
            
            # Convert combined PIL image to base64 PNG
            img_byte_arr = io.BytesIO()
            combined_image_pil.save(img_byte_arr, format='PNG')
            img_bytes = img_byte_arr.getvalue()
            
            base64_image = base64.b64encode(img_bytes).decode("utf-8")
            combined_base64_images.append(base64_image)

        pdf_document.close()

        LOGGER.info(f"Successfully processed {len(combined_base64_images)} combined images from PDF.")

        return combined_base64_images
    except Exception as e:
        return []
    
def bmp_bytes_to_base64_png_string(bmp_bytes):
    """
    Converts BMP image bytes to a base64 encoded PNG string.
    Returns a list containing one base64 string, or an empty list on error.
    """
    if not bmp_bytes:
        LOGGER.error("Received empty bmp_bytes in bmp_bytes_to_base64_png_string.")
        return []
    try:
        img = Image.open(io.BytesIO(bmp_bytes))
        if img.mode == 'P':
            img = img.convert('RGB')
        elif img.mode == 'RGBA':
            pass
        elif img.mode != 'RGB':
            img = img.convert('RGB')

        png_buffer = io.BytesIO()
        img.save(png_buffer, format="PNG")
        png_buffer.seek(0)
        png_bytes = png_buffer.getvalue()
        base64_png_string = base64.b64encode(png_bytes).decode('utf-8')
        LOGGER.info(f"Converted BMP to base64 PNG string successfully.")
        return [base64_png_string]
    except Exception as e:
        LOGGER.error(f"Error converting BMP to base64 PNG: {e}")
        return []
