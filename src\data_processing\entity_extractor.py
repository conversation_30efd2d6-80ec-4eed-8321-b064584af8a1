import random
import string
import asyncio
from src.services.llm_handler import LLMHandler

from src.utils.logger import get_logger
LOGGER = get_logger("src.data_processing.entity_extractor")

# Constants for generating unique IDs for markdown tables
HEX_CHARS = string.hexdigits.lower()
RANDOM_ID_LENGTH = 5

async def extract_investment_entities(llm_handler: LLMHandler, base64_images: list) -> dict:
    """
    Extracts investment entities and returns the full response object including tokens.
    """
    LOGGER.info("Extracting initial investment entities...")
    response = await llm_handler.get_json_from_images(
        template_name='extract_entities.j2',
        context={},
        base64_images=base64_images,
        default_response={"investment_management_entity": []}
    )

    # Post-processing: Assign a unique ID to each extracted entity.
    if "investment_management_entity" in response["result"]:
        for i, entity in enumerate(response["result"]["investment_management_entity"]):
            entity["id"] = f"IME_{i + 1:03d}"
    
    return response

async def remove_duplicate_entities(llm_handler: <PERSON><PERSON><PERSON><PERSON><PERSON>, base64_images: list, investment_entities: dict) -> dict:
    """
    Consolidates duplicates and returns the full response object including tokens.
    """
    LOGGER.info("Removing duplicate investment entities...")
    context = {"investment_entities": investment_entities}

    response = await llm_handler.get_json_from_images(
        template_name='remove_duplicate_entities.j2',
        context=context,
        base64_images=base64_images,
        default_response=investment_entities
    )

    if "investment_management_entity" in response["result"]:
        for i, entity in enumerate(response["result"]["investment_management_entity"]):
            entity["id"] = f"IME_{i + 1:03d}"
            
    return response

async def extract_dates(llm_handler: LLMHandler, base64_images: list, investment_entities: dict) -> dict:
    """
    Extracts dates and returns the result along with the sum of tokens used.
    """
    LOGGER.info("Extracting dates for each entity...")
    all_dates = {"date": []}
    total_tokens_step = 0
    date_id_counter = 1
    tasks = []
    task_to_entity_map = {}

    async with asyncio.TaskGroup() as tg:
        for entity in investment_entities.get("investment_management_entity", []):
            entity_details = f"investment entity name: {entity.get('name')}, investment entity id: {entity.get('id')}"
            context = {"entity_details": entity_details}
            coroutine = llm_handler.get_json_from_images(
                template_name='extract_dates.j2', context=context,
                base64_images=base64_images, default_response={"date": []}
            )
            task = tg.create_task(coroutine)
            tasks.append(task)
            task_to_entity_map[task] = entity

    for task in tasks:
        if not task or not task.result(): continue
        
        task_response = task.result()
        task_result = task_response["result"]
        total_tokens_step += task_response.get("tokens", 0)
        entity = task_to_entity_map[task]

        if task_result.get("date"):
            for date_entry in task_result["date"]:
                date_entry["date_id"] = f"DATE_{date_id_counter:03d}"
                date_entry["ime_id"] = entity.get("id")
                date_id_counter += 1
            all_dates["date"].extend(task_result.get("date"))
            
    return {"result": all_dates, "tokens": total_tokens_step}

def map_dates_to_entities(dates_entity: dict, investment_entities: dict) -> dict:
    """
    Maps the extracted date IDs back to their corresponding investment entities.
    This is a pure data transformation step, no LLM call.
    """
    ime_to_date_id_map = {date['ime_id']: date['date_id'] for date in dates_entity.get('date', [])}
    for entity in investment_entities.get('investment_management_entity', []):
        entity['date_id'] = ime_to_date_id_map.get(entity.get('id'))
    return investment_entities

async def extract_ime_accounts(llm_handler: LLMHandler, base64_images: list, investment_entities: dict) -> dict:
    """
    Extracts all investment management entity (IME) accounts from the document.
    """
    LOGGER.info("Extracting IME accounts...")
    context = {"investment_entities": investment_entities}
    response = await llm_handler.get_json_from_images(
        template_name='extract_ime_accounts.j2', context=context,
        base64_images=base64_images, default_response={"ime_account": []}
    )
    if response["result"].get("ime_account"):
        for i, account in enumerate(response["result"]["ime_account"]):
            account["id"] = f"IMEACC_{i + 1:03d}"
    return response

async def extract_plan_sponsors(llm_handler: LLMHandler, base64_images: list, investment_entities: dict, ime_accounts_entity: dict) -> dict:
    """
    Extracts plan sponsor entities, which are linked to investment entities.
    """
    LOGGER.info("Extracting plan sponsors...")
    if any(acc.get('margin_account') == 'yes' for acc in ime_accounts_entity.get('ime_account', [])):
        LOGGER.info("Margin account found, skipping plan sponsor extraction.")
        return {"result": {"plan_sponsor": []}, "tokens": 0}

    all_sponsors = {"plan_sponsor": []}
    total_tokens_step = 0
    sponsor_id_counter = 1
    tasks = []
    task_to_entity_map = {}

    async with asyncio.TaskGroup() as tg:
        for entity in investment_entities.get("investment_management_entity", []):
            entity_details = f"investment entity name: {entity.get('name')}, investment entity id: {entity.get('id')}"
            context = {"entity_details": entity_details}
            coroutine = llm_handler.get_json_from_images(
                template_name='extract_plan_sponsors.j2', context=context,
                base64_images=base64_images, default_response={"plan_sponsor": []}
            )
            task = tg.create_task(coroutine)
            tasks.append(task)
            task_to_entity_map[task] = entity

    for task in tasks:
        if not task or not task.result(): continue
        
        task_response = task.result()
        task_result = task_response["result"]
        total_tokens_step += task_response.get("tokens", 0)
        entity = task_to_entity_map[task]

        if task_result.get("plan_sponsor"):
            for sponsor in task_result["plan_sponsor"]:
                sponsor["id"] = f"PS_{sponsor_id_counter:03d}"
                sponsor["ime_id"] = entity.get("id")
                sponsor_id_counter += 1
            all_sponsors["plan_sponsor"].extend(task_result["plan_sponsor"])

    return {"result": all_sponsors, "tokens": total_tokens_step}

async def extract_ps_accounts(llm_handler: LLMHandler, base64_images: list, plan_sponsor_entity: dict) -> dict:
    """
    Extracts all plan sponsor entity (PSE) accounts from the document.
    """
    if not plan_sponsor_entity.get("plan_sponsor"):
        return {"result": {"ps_account": []}, "tokens": 0}
        
    LOGGER.info("Extracting Plan Sponsor accounts...")
    context = {"plan_sponsor_entity": plan_sponsor_entity}
    response = await llm_handler.get_json_from_images(
        template_name='extract_ps_accounts.j2', context=context,
        base64_images=base64_images, default_response={"ps_account": []}
    )
    if response["result"].get("ps_account"):
        for i, account in enumerate(response["result"]["ps_account"]):
            account["id"] = f"PSACC_{i + 1:03d}"
    return response

async def relate_ps_accounts_to_ime_accounts(llm_handler: LLMHandler, base64_images: list, ime_accounts: dict, pse_accounts: dict) -> dict:
    """
    Determines the relationship between Plan Sponsor accounts and IME accounts.
    """
    if not ime_accounts.get("ime_account"):
        for ps_account in pse_accounts.get("ps_account", []):
            ps_account["imeacc_id"], ps_account["ime_name"] = None, None
        return {"result": pse_accounts, "tokens": 0}

    LOGGER.info("Relating PS accounts to IME accounts...")
    context = {"ime_accounts": ime_accounts, "pse_accounts": pse_accounts}
    return await llm_handler.get_json_from_images(
        template_name='relate_accounts.j2',
        context=context,
        base64_images=base64_images,
        default_response=pse_accounts
    )

async def extract_holdings_as_markdown(llm_handler: LLMHandler, base64_images: list, **kwargs) -> tuple[str, int]:
    """
    Iterates through entities and accounts to extract all holdings tables as a single markdown string.
    The **kwargs dictionary contains all previously extracted data.
    """
    LOGGER.info("Extracting holdings tables as markdown...")
    total_tokens_step = 0
    tasks, results = [], []
    
    investment_entities = kwargs.get('investment_entities', {})
    plan_sponsor_entity = kwargs.get('plan_sponsor_entity', {})
    pse_accounts_entity = kwargs.get('pse_accounts_entity', {})
    ime_accounts_entity = kwargs.get('ime_accounts_entity', {})

    sponsors_by_ime_id = {s["ime_id"]: s for s in plan_sponsor_entity.get("plan_sponsor", [])}
    pse_accounts_by_sponsor_id = {p["ps_id"]: p for p in pse_accounts_entity.get("ps_account", [])}
    ime_accounts_by_ime_id = {i["ime_id"]: i for i in ime_accounts_entity.get("ime_account", [])}

    async with asyncio.TaskGroup() as tg:
        for idx, entity in enumerate(investment_entities.get("investment_management_entity", [])):
            entity_id = entity["id"]
            sponsor = sponsors_by_ime_id.get(entity_id)
            
            context = {}
            if sponsor:
                pse_account = pse_accounts_by_sponsor_id.get(sponsor["id"])
                if pse_account:
                    context = {
                        "name": entity.get("name"), "entity_id": entity_id,
                        "ime_account_type": pse_account.get("ime_name"), "ime_account_id": pse_account.get("imeacc_id"),
                        "sponsor_name": sponsor.get("name"), "sponsor_id": sponsor.get("id"),
                        "plan_account": pse_account.get("name"), "plan_number": pse_account.get("number"),
                        "plan_descriptor": pse_account.get("descriptor"), "pse_account_id": pse_account.get("id"),
                    }
            elif entity_id in ime_accounts_by_ime_id:
                ime_account = ime_accounts_by_ime_id[entity_id]
                context = {
                    "name": entity.get("name"), "entity_id": entity_id,
                    "ime_account_type": ime_account.get("name"), "ime_account_id": ime_account.get("id"),
                    "ime_descriptor": ime_account.get("descriptor"),
                }
            else:
                context = {"name": entity.get("name"), "entity_id": entity_id}

            custom_id = ''.join(random.choices(HEX_CHARS, k=RANDOM_ID_LENGTH))
            context["table_id"] = f"holdings_{custom_id}_"

            coroutine = llm_handler.get_markdown_from_images(
                template_name='extract_holdings_to_markdown.j2',
                context=context,
                base64_images=base64_images
            )
            task = tg.create_task(coroutine)
            task.set_name(str(idx))
            tasks.append(task)

    for task in tasks:
        if not task or not task.result(): continue
        markdown_text, tokens = task.result()
        total_tokens_step += tokens
        results.append([int(task.get_name()), markdown_text])

    sorted_results = sorted(results, key=lambda x: x[0])
    markdown_parts = [res[1] for res in sorted_results]
    return "\n\n".join(markdown_parts), total_tokens_step

async def extract_holdings_from_markdown(llm_handler: LLMHandler, markdown_chunks: list) -> dict:
    """
    Processes markdown chunks, each containing holdings tables, and extracts structured JSON.
    """
    all_holdings = {"holding": []}
    total_tokens_step = 0
    tasks = []
    if not markdown_chunks:
        return {"result": all_holdings, "tokens": 0}
        
    LOGGER.info(f"Extracting holdings from {len(markdown_chunks)} markdown chunks...")
    async with asyncio.TaskGroup() as tg:
        for chunk in markdown_chunks:
            context = {"chunk": chunk}
            coroutine = llm_handler.get_json_from_text(
                template_name='extract_holdings_from_markdown.j2',
                context=context,
                default_response={"holding": []}
            )
            tasks.append(tg.create_task(coroutine))

    for task in tasks:
        if not task or not task.result(): continue
        response = task.result()
        total_tokens_step += response.get("tokens", 0)
        all_holdings["holding"].extend(response["result"].get("holding", []))

    for i, holding in enumerate(all_holdings["holding"]):
        holding["id"] = f"H_{i + 1:03d}"
        
    return {"result": all_holdings, "tokens": total_tokens_step}

async def extract_transactions_as_markdown(llm_handler: LLMHandler, base64_images: list, **kwargs) -> tuple[str, int]:
    """
    Iterates through entities and accounts to extract all transaction tables as a single markdown string.
    """
    LOGGER.info("Extracting transaction tables as markdown...")
    total_tokens_step = 0
    tasks, responses = [], []
    
    investment_entities = kwargs.get('investment_entities', {})
    plan_sponsor_entity = kwargs.get('plan_sponsor_entity', {})
    pse_accounts_entity = kwargs.get('pse_accounts_entity', {})
    ime_accounts_entity = kwargs.get('ime_accounts_entity', {})

    sponsors_by_ime_id = {s["ime_id"]: s for s in plan_sponsor_entity.get("plan_sponsor", [])}
    pse_accounts_by_sponsor_id = {p["ps_id"]: p for p in pse_accounts_entity.get("ps_account", [])}
    ime_accounts_by_ime_id = {i["ime_id"]: i for i in ime_accounts_entity.get("ime_account", [])}

    async with asyncio.TaskGroup() as tg:
        for idx, entity in enumerate(investment_entities.get("investment_management_entity", [])):
            entity_id = entity["id"]
            sponsor = sponsors_by_ime_id.get(entity_id)
            
            context = {}
            if sponsor:
                pse_account = pse_accounts_by_sponsor_id.get(sponsor["id"])
                if pse_account:
                    context = {
                        "name": entity.get("name"), "entity_id": entity_id,
                        "ime_account_type": pse_account.get("ime_name"), "ime_account_id": pse_account.get("imeacc_id"),
                        "sponsor_name": sponsor.get("name"), "sponsor_id": sponsor.get("id"),
                        "plan_account": pse_account.get("name"), "plan_number": pse_account.get("number"),
                        "pse_account_descriptor": pse_account.get("descriptor"), "pse_account_id": pse_account.get("id"),
                    }
            elif entity_id in ime_accounts_by_ime_id:
                ime_account = ime_accounts_by_ime_id[entity_id]
                context = {
                    "name": entity.get("name"), "entity_id": entity_id,
                    "ime_account_type": ime_account.get("name"), "ime_account_id": ime_account.get("id"),
                    "ime_account_descriptor": ime_account.get("descriptor"),
                }
            else:
                context = {"name": entity.get("name"), "entity_id": entity_id}

            custom_id = ''.join(random.choices(HEX_CHARS, k=RANDOM_ID_LENGTH))
            context["table_id"] = f"transactions_{custom_id}_"

            coroutine = llm_handler.get_markdown_from_images(
                template_name='extract_transactions_to_markdown.j2',
                context=context,
                base64_images=base64_images
            )
            task = tg.create_task(coroutine)
            task.set_name(str(idx))
            tasks.append(task)

    for task in tasks:
        if not task or not task.result(): continue
        markdown_text, tokens = task.result()
        total_tokens_step += tokens
        responses.append([task.get_name(), markdown_text])

    sorted_results = sorted(responses, key=lambda x: x[0])
    markdown_parts = [res[1] for res in sorted_results]
    return "\n\n".join(markdown_parts), total_tokens_step

async def extract_transactions_from_markdown(llm_handler: LLMHandler, markdown_chunks: list) -> dict:
    """
    Processes markdown chunks, each containing transaction tables, and extracts structured JSON.
    """
    all_transactions = {"transaction": []}
    total_tokens_step = 0
    tasks = []
    if not markdown_chunks:
        return {"result": all_transactions, "tokens": 0}

    LOGGER.info(f"Extracting transactions from {len(markdown_chunks)} markdown chunks...")
    async with asyncio.TaskGroup() as tg:
        for idx, chunk in enumerate(markdown_chunks):
            context = {"chunk": chunk}
            coroutine = llm_handler.get_json_from_text(
                template_name='extract_transactions_from_markdown.j2',
                context=context,
                default_response={"transaction": []}
            )
            task = tg.create_task(coroutine)
            task.set_name(str(idx))
            tasks.append(task)
    
    for task in tasks:
        if not task or not task.result(): continue
        response = task.result()
        total_tokens_step += response.get("tokens", 0)
        all_transactions["transaction"].extend(response["result"].get("transaction", []))

    for i, transaction in enumerate(all_transactions["transaction"]):
        transaction["id"] = f"T_{i + 1:03d}"

    return {"result": all_transactions, "tokens": total_tokens_step}