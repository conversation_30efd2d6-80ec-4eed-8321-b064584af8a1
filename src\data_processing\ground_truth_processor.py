import os
import pandas as pd
from openpyxl import load_workbook
import re
import numpy as np
import asyncio
import concurrent.futures

from src.utils.logger import get_logger
LOGGER = get_logger("src.data_processing.ground_truth_processor")

def get_metadata(ws):
    target_value = "File Name:"
    col_b = [cell.value for cell in ws['B']]
    row_index = next((i + 1 for i, val in enumerate(col_b) if val == target_value), None)
    if row_index is None:
        return None, None
    file_name_sheet = ws[f"C{row_index}"].value
    file_url_sheet = ws[f"C{row_index+2}"].value
    return file_name_sheet, file_url_sheet

def get_ranges(range_str:str):
    start, end = range_str.split(':')
    start_col = re.split(r'\d+', start)[0]
    start_row = re.split(r'[A-Za-z]+', start)[-1]
    end_col = re.split(r'\d+', end)[0]
    end_row = re.split(r'[A-Za-z]+', end)[-1]
    return start_col, start_row, end_col, end_row

def get_column_names(columns:list, category:str):
    dict_cols = {}
    if category == "IME":
        dict_cols = {
            "ID": "IME ID",
            "Name": "IME Name",
            "Address": "IME Address",
            "Website": "IME Website",
            "Entity Unique Ids": "IME Unique Ids",
            "Start Date": "Start Date",
            "End Date": "End Date"
        }
    elif category == "PS":
        dict_cols = {
            "ID": "PS ID",
            "Name": "PS Name",
            "Address": "PS Address",
            "Website": "PS Website",
            "Entity Unique Ids": "PS Unique Ids",
            "Inv. Mgmt. Entity": "IME ID"
        }
    elif category == "Accounts":
        dict_cols = {
            "ID": "Account ID",
            "Name": "Account Name",
            "Number": "Account Number",
            "Inv. Mgmt. Entity": "IME ID"
        }
    elif category == "Plans":
        dict_cols = {
            "ID": "Plan ID",
            "Name": "Plan Name",
            "Number": "Plan Number",
            "Plan Sponsor": "PS ID",
            "Inv. Mgmt. Entity Account": "Account ID"
        }
    elif category == "Holdings":
        dict_cols = {
            "ID": "ID",
            "Name": "Name",
            "Ticker": "Ticker",
            "Class": "Class",
            "Type": "Class/Type",
            "Exchange": "Exchange",
            "Currency": "Currency",
            "Beginning Value": "Holding Beginning Value",
            "Ending Value": "Holding Ending Value",
            "Security Unique ID": "Unique ID",
            "Holding Table ID": "Table ID",
            "Account or Inv. Mgmt. Entity": "Account ID",
            "Inv. Mgmt. Entity": "IME ID",
            "Plan or Plan Sponsor": "Plan ID",
            "Plan Sponsor": "PS ID"
        }
    elif category == "Transactions":
        dict_cols = {
            "ID": "ID",
            "Name": "Name",
            "Ticker": "Ticker",
            "Class": "Class",
            "Type": "Class/Type",
            "Exchange": "Exchange",
            "Currency": "Currency",
            "Investment Value": "Investment Value",
            "Transaction Type": "Transaction Type",
            "Transaction Date": "Transaction Date",
            "Security Unique ID": "Unique ID",
            "Transaction Table ID": "Table ID",
            "Account or Inv. Mgmt. Entity": "Account ID",
            "Inv. Mgmt. Entity": "IME ID",
            "Plan or Plan Sponsor": "Plan ID",
            "Plan Sponsor": "PS ID"
        }


    return [dict_cols[col] if col in dict_cols else col for col in columns]

def get_tables(tables, sheet_name:str, file_path_or_buffer):
    """
    Extracts data from named tables within a specific Excel sheet into DataFrames.
    """
    dict_df = {}
    
    for table_name in tables: 
        
        table = tables[table_name]
        
        table_cat = re.split(r"[\d\_]+", table_name)[0]
        
        table_range = table.ref
        
        start_col, start_row, end_col, end_row = get_ranges(table_range)

        cols_str = f"{start_col}:{end_col}"
        skip_rows = int(start_row) - 1
        num_rows = int(end_row) - int(start_row) + 1
        
        df_pre = pd.read_excel(
            file_path_or_buffer,
            sheet_name=sheet_name,
            usecols=cols_str,
            skiprows=skip_rows,
            nrows=num_rows,
            dtype=str,
        )

        df_pre.columns = get_column_names(table.column_names, table_cat)
        
        df_pre_flag = df_pre.loc[:,df_pre.columns[1:]].isna().all(axis=1)
        df_pre = df_pre[df_pre_flag == False]

        #id_cols = [col for col in df_pre.columns if col.endswith(" ID")]
        #df_pre[id_cols] = df_pre[id_cols].fillna("None")
        
        # Ensure the category exists before assigning
        if table_cat not in dict_df:
            dict_df[table_cat] = pd.DataFrame()

        dict_df[table_cat] = pd.concat([dict_df[table_cat], df_pre], ignore_index=True)

    return dict_df

def table_general(dict_df: dict[str,pd.DataFrame]):
    df_ime = dict_df["IME"]
    df_acc = dict_df["Accounts"]
    df_ps = dict_df["PS"]
    df_plan = dict_df["Plans"]

    df_ime.replace(
        to_replace=["null", "None", "nan", "NaN", "N/A", ""],
        value=None, inplace=True
    )
    df_acc.replace(
        to_replace=["null", "None", "nan", "NaN", "N/A", ""],
        value=None, inplace=True
    )
    df_ps.replace(
        to_replace=["null", "None", "nan", "NaN", "N/A", ""],
        value=None, inplace=True
    )
    df_plan.replace(
        to_replace=["null", "None", "nan", "NaN", "N/A", ""],
        value=None, inplace=True
    )

    df_plan["Account ID"] = df_plan["Account ID"].apply(lambda x: x if type(x) == str and x.startswith("IMEACC") else None)

    ### IME with account
    if not (df_ime.empty and df_acc.empty):
        ime_acc = pd.merge(
            left=df_ime,
            right=df_acc,
            on=["IME ID"],
            how="outer"
        )

        #id_cols = [col for col in ime_acc.columns if col.endswith(" ID")]
        #ime_acc[id_cols] = ime_acc[id_cols].fillna("None")
        
        if not df_acc.empty:
            ime_acc = pd.concat([ime_acc, df_ime], axis=0)
            #ime_acc[id_cols] = ime_acc[id_cols].fillna("None")
        
        if not df_ime.empty:
            ime_acc = pd.concat([ime_acc, df_acc], axis=0)
            #ime_acc[id_cols] = ime_acc[id_cols].fillna("None")

    else:
        columns = pd.concat([df_acc, df_ime], axis=1).columns.unique()
        ime_acc = pd.DataFrame(columns=columns)

    ### Plan Sponsor with Plans
    if not (df_ps.empty and df_plan.empty):
        ps_plan = pd.merge(
            left=df_ps,
            right=df_plan,
            on=["PS ID"],
            how="outer"
        )
        
        #id_cols = [col for col in ps_plan.columns if col.endswith(" ID")]
        #ps_plan[id_cols] = ps_plan[id_cols].fillna("None")
        
        if not df_plan.empty:
            ps_plan = pd.concat([ps_plan, df_ps], axis=0)
            #ps_plan[id_cols] = ps_plan[id_cols].fillna("None")
        
        if not df_ps.empty:
            ps_plan = pd.concat([ps_plan, df_plan], axis=0)
            #ps_plan[id_cols] = ps_plan[id_cols].fillna("None")
        
    else:
        columns = pd.concat([df_ps, df_plan], axis=1).columns.unique()
        ps_plan = pd.DataFrame(columns=columns)

    ### Investment Management Entity with Accounts + Plan Sponsor with Plans
    if not (ime_acc.empty and ps_plan.empty):
        ime_acc_ps_plan = pd.merge(
            left=ime_acc,
            right=ps_plan,
            on=["IME ID", "Account ID"],
            how="outer"
        )
        if not ps_plan.empty:
            ime_acc_ps_plan = pd.concat([ime_acc_ps_plan, ime_acc], axis=0)
            #ime_acc_ps_plan[id_cols] = ime_acc_ps_plan[id_cols].fillna("None")
        if not ime_acc.empty:
            ime_acc_ps_plan = pd.concat([ime_acc_ps_plan, ps_plan], axis=0)
            #ime_acc_ps_plan[id_cols] = ime_acc_ps_plan[id_cols].fillna("None")
    else:
        columns = pd.concat([ime_acc, ps_plan], axis=1).columns.unique()
        ime_acc_ps_plan = pd.DataFrame(columns=columns)

    return ime_acc_ps_plan.drop_duplicates()

def table_holdings_transactions(dict_df: dict[str,pd.DataFrame]):
    df_h = dict_df.get("Holdings", pd.DataFrame())
    df_t = dict_df.get("Transactions", pd.DataFrame())

    if not df_h.empty and "Account ID" in df_h.columns:
        df_h["Account ID"] = df_h["Account ID"].apply(
            lambda x: x if isinstance(x, str) and x.startswith("IMEACC") else None
        )
    
    if not df_t.empty and "Account ID" in df_t.columns:
        df_t["Account ID"] = df_t["Account ID"].apply(
            lambda x: x if isinstance(x, str) and x.startswith("IMEACC") else None
        )

    if not (df_h.empty and df_t.empty):
        df_h.insert(0, "Nature", "Holding")
        df_t.insert(0, "Nature", "Transaction")
        
    df_holdings_transactions = pd.concat([df_h, df_t], axis=0)

    return df_holdings_transactions
"""
def improve_ids(x):
    try:
        if not pd.isna(x):
            num = x[-3:]
            lit = x[:-3]
            return f"{lit}{int(num)}"
        
        else: 
            return x
    except Exception as e:
        print(f"Error processing {x}: {e}")
        return x
"""

def process_single_sheet(sheet_name, wb, file_path_or_buffer):
    """
    Processes a single sheet from the Excel workbook.
    This function will be executed in a separate thread for each sheet.

    Args:
        sheet_name (str): The name of the sheet to process.
        wb (Workbook): The loaded openpyxl workbook object.
        file_path_or_buffer: The file buffer to be passed to pandas.

    Returns:
        tuple: A tuple containing (df_general_sheet, df_holdings_transactions_sheet) for this sheet.
    """
    ws = wb[sheet_name]
    file_name_sheet, file_url_sheet = get_metadata(ws)
    
    if not file_name_sheet:
        LOGGER.warning(f"Could not find 'File Name:' in sheet '{sheet_name}'. Skipping.")
        return None, None
        
    LOGGER.info(f"Processing sheet: {sheet_name} for file: {file_name_sheet} in a parallel thread.")
    
    tables = ws.tables
    if not tables:
        LOGGER.warning(f"No tables found in sheet {sheet_name}. Skipping.")
        return None, None

    dict_df = get_tables(tables, sheet_name, file_path_or_buffer)

    df_g = table_general(dict_df)
    df_g.insert(0, "file_name", file_name_sheet)
    df_g.insert(1, "file_url", file_url_sheet)
    df_g.insert(2, "sheet_name", sheet_name)
    
    df_ht = table_holdings_transactions(dict_df)
    df_ht.insert(0, "file_name", file_name_sheet)
    df_ht.insert(1, "file_url", file_url_sheet)
    df_ht.insert(2, "sheet_name", sheet_name)
    
    return df_g, df_ht

def process_ground_truth_excel(file_path_or_buffer):
    """
    Reads a ground truth Excel file and processes its sheets in parallel to speed up the extraction.

    Args:
        file_path_or_buffer: The path to the Excel file or a file-like object.

    Returns:
        tuple: A tuple containing the combined (df_general, df_holdings_transactions).
    """
    LOGGER.info("Starting the parallel processing of the ground truth Excel file.")
    
    general_dfs = []
    ht_dfs = []

    wb = load_workbook(file_path_or_buffer, data_only=True)
    sheet_names = [s for s in wb.sheetnames if s not in ["Template", "LISTS"]]

    with concurrent.futures.ThreadPoolExecutor(max_workers=os.cpu_count()) as executor:
        # Create a future for each sheet processing task
        future_to_sheet = {executor.submit(process_single_sheet, name, wb, file_path_or_buffer): name for name in sheet_names}
        
        for future in concurrent.futures.as_completed(future_to_sheet):
            sheet_name = future_to_sheet[future]
            try:
                df_g, df_ht = future.result()
                if df_g is not None and not df_g.empty:
                    general_dfs.append(df_g)
                if df_ht is not None and not df_ht.empty:
                    ht_dfs.append(df_ht)
            except Exception as exc:
                LOGGER.error(f"Sheet '{sheet_name}' generated an exception: {exc}")

    # Combine the results from all threads
    df_general = pd.concat(general_dfs, ignore_index=True) if general_dfs else pd.DataFrame()
    df_holdings_transactions = pd.concat(ht_dfs, ignore_index=True) if ht_dfs else pd.DataFrame()

    # Reset buffer for any subsequent reads if it's a file-like object
    if hasattr(file_path_or_buffer, 'seek'):
        file_path_or_buffer.seek(0)

    # Final cleanup
    null_variants = ["None", "null", "nan", "NaN", "N/A", "n/a", "NA", ""]
    if not df_holdings_transactions.empty:
        df_holdings_transactions.replace(to_replace=null_variants, value=None, inplace=True)
    if not df_general.empty:
        df_general.replace(to_replace=null_variants, value=None, inplace=True)
        
    LOGGER.info(f"Parallel ground truth processing finished. Processed {len(general_dfs)} sheets successfully.")
    return df_general, df_holdings_transactions

async def async_process_ground_truth_excel(file_path_or_buffer):
    """
    Asynchronously processes the ground truth Excel file.
    The underlying synchronous function now runs the sheet processing in parallel.
    """
    LOGGER.info("Scheduling the parallel Excel processing in a separate thread.")
    loop = asyncio.get_running_loop()
    # This function call now executes the new, faster, parallel version.
    df_general, df_holdings_transactions = await loop.run_in_executor(
        None, process_ground_truth_excel, file_path_or_buffer
    )
    LOGGER.info("Asynchronous wrapper finished: parallel Excel processing is complete.")
    return df_general, df_holdings_transactions

def create_field(value):
    if pd.isna(value) or value in ["None", "nan", "NaN", "N/A", "n/a", "NA", ""]:
        return {"value": None, "page_number": None, "confidence": None, "bounding_regions": None}
    return {"value": str(value), "page_number": 1, "confidence": 1.0, "bounding_regions": []}

def transform_gt_to_llm_format(df_general, df_holdings_transactions):
    """
    Transforms the ground truth DataFrames into the format expected by the LLM.
    """
    all_documents_json = {}
    
    # id_cols_general = [c for c in df_general.columns if "ID" in c]
    # df_general[id_cols_general] = df_general[id_cols_general].fillna('None')
    # id_cols_ht = [c for c in df_holdings_transactions.columns if "ID" in c]
    # df_holdings_transactions[id_cols_ht] = df_holdings_transactions[id_cols_ht].fillna('None')

    for file_name, doc_general_group in df_general.groupby('file_name'):
        file_url = doc_general_group['file_url'].iloc[0]
        output_json = {
            "document_id": file_name, "document_url": file_url,
            "investment_management_entity": [], "date": [], "plan_sponsor": [],
            "ime_account": [], "ps_account": [], "holding": [], "transaction": []
        }

        # 1. Investment Management Entity (IME) & Dates
        ime_cols = ['IME ID', 'IME Name', 'IME Address', 'IME Website', 'IME Unique Ids']
        if 'Start Date' in doc_general_group.columns: ime_cols.append('Start Date')
        if 'End Date' in doc_general_group.columns: ime_cols.append('End Date')
        
        ime_df = doc_general_group[ime_cols].drop_duplicates(subset=['IME ID'])
        date_counter = 1

        for _, row in ime_df.iterrows():
            if pd.notna(row['IME ID']):
                output_json['investment_management_entity'].append({
                    "id": row['IME ID'],
                    "data": {
                        "name": create_field(row['IME Name']),
                        "address": create_field(row['IME Address']),
                        "website": create_field(row['IME Website']),
                        "entity_unique_id": create_field(row['IME Unique Ids'])
                    }
                })
                start_date_val, end_date_val = row.get('Start Date'), row.get('End Date')
                if pd.notna(start_date_val) or pd.notna(end_date_val):
                    output_json['date'].append({
                        "id": f"DATE_{date_counter:03d}", "ime_id": row['IME ID'],
                        "data": {"start_date": create_field(start_date_val), "end_date": create_field(end_date_val)}
                    })
                    date_counter += 1

        # 2. Plan Sponsor (PS)
        ps_df = doc_general_group[['PS ID', 'PS Name', 'PS Address', 'PS Website', 'PS Unique Ids', 'IME ID']].drop_duplicates(subset=['PS ID'])
        for _, row in ps_df.iterrows():
            if pd.notna(row['PS ID']):
                output_json['plan_sponsor'].append({
                    "id": row['PS ID'],
                    "ime_id": row['IME ID'] if pd.notna(row['IME ID']) else None,
                    "data": {
                        "name": create_field(row['PS Name']),
                        "address": create_field(row['PS Address']),
                        "website": create_field(row['PS Website']),
                        "entity_unique_id": create_field(row['PS Unique Ids'])
                    }
                })
        
        # 3. IME Account (Accounts)
        acc_df = doc_general_group[['Account ID', 'Account Name', 'Account Number', 'IME ID']].drop_duplicates(subset=['Account ID'])
        for _, row in acc_df.iterrows():
            if pd.notna(row['Account ID']):
                 output_json['ime_account'].append({
                    "id": row['Account ID'],
                    "ime_id": row['IME ID'] if pd.notna(row['IME ID']) else None,
                    "data": {
                        "name": create_field(row['Account Name']),
                        "number": create_field(row['Account Number'])
                    }
                })

        # 4. PS Account (Plans)
        plan_df = doc_general_group[['Plan ID', 'Plan Name', 'Plan Number', 'PS ID', 'Account ID']].drop_duplicates(subset=['Plan ID'])
        for _, row in plan_df.iterrows():
            if pd.notna(row['Plan ID']):
                output_json['ps_account'].append({
                    "id": row['Plan ID'],
                    "imeacc_id": row['Account ID'] if pd.notna(row['Account ID']) else None,
                    "ps_id": row['PS ID'] if pd.notna(row['PS ID']) else None,
                    "data": {
                        "name": create_field(row['Plan Name']),
                        "number": create_field(row['Plan Number'])
                    }
                })

        doc_ht_group = df_holdings_transactions[df_holdings_transactions['file_name'] == file_name]

        # Comprueba si el DataFrame tiene datos y la columna 'Nature' antes de filtrar
        if not doc_ht_group.empty and 'Nature' in doc_ht_group.columns:
            holdings_df = doc_ht_group[doc_ht_group['Nature'] == 'Holding']
            transactions_df = doc_ht_group[doc_ht_group['Nature'] == 'Transaction']
        else:
            # Si no, crea DataFrames vacíos para evitar errores
            holdings_df = pd.DataFrame()
            transactions_df = pd.DataFrame()

        # 5. Holdings
        for _, row in holdings_df.iterrows():
            if pd.notna(row['ID']):
                output_json['holding'].append({
                    "id": row['ID'],
                    "ime_id": row['IME ID'] if pd.notna(row['IME ID']) else None,
                    "imeacc_id": row['Account ID'] if pd.notna(row['Account ID']) else None,
                    "ps_id": row['PS ID'] if pd.notna(row['PS ID']) else None,
                    "psacc_id": row['Plan ID'] if pd.notna(row['Plan ID']) else None,
                    "ht_id": row['Table ID'] if pd.notna(row['Table ID']) else None,
                    "data": {
                        "name": create_field(row['Name']), "ticker": create_field(row['Ticker']),
                        "class": create_field(row['Class']), "type": create_field(row.get('Class/Type')),
                        "exchange": create_field(row['Exchange']), "currency": create_field(row['Currency']),
                        "beginning_value": create_field(row['Holding Beginning Value']),
                        "ending_value": create_field(row['Holding Ending Value']),
                        "security_unique_id": create_field(row['Unique ID'])
                    }
                })

        # 6. Transactions
        for _, row in transactions_df.iterrows():
            if pd.notna(row['ID']):
                output_json['transaction'].append({
                    "id": row['ID'],
                    "ime_id": row['IME ID'] if pd.notna(row['IME ID']) else None,
                    "imeacc_id": row['Account ID'] if pd.notna(row['Account ID']) else None,
                    "ps_id": row['PS ID'] if pd.notna(row['PS ID']) else None,
                    "psacc_id": row['Plan ID'] if pd.notna(row['Plan ID']) else None,
                    "tt_id": row['Table ID'] if pd.notna(row['Table ID']) else None,
                    "data": {
                        "name": create_field(row['Name']), "transaction_date": create_field(row['Transaction Date']),
                        "transaction_type": create_field(row['Transaction Type']), "ticker": create_field(row['Ticker']),
                        "class": create_field(row['Class']), "type": create_field(row.get('Class/Type')),
                        "exchange": create_field(row['Exchange']), "currency": create_field(row['Currency']),
                        "investment_value": create_field(row['Investment Value']),
                        "security_unique_id": create_field(row['Unique ID'])
                    }
                })
            
        all_documents_json[file_name] = output_json
        
    return all_documents_json