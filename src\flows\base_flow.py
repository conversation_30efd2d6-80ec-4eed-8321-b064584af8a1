from abc import ABC, abstractmethod

class BaseFlow(ABC):
    """
    Abstract base class for document processing flows.
    All flow implementations must inherit from this class and implement the process_document method.
    """
    
    def __init__(self, llm_handler, document_intelligence_client=None):
        """
        Initialize the flow with required services.
        
        Args:
            llm_handler: Handler for LLM interactions
            document_intelligence_client: Client for document intelligence (optional)
        """
        self.llm_handler = llm_handler
        self.document_intelligence_client = document_intelligence_client

    @abstractmethod
    async def async_process_document(self, file_url: str, file_name: str, json_result: dict = None):
        """
        Process a document asynchronously and return structured data.
        
        Args:
            file_url: URL of the document to process
            file_name: Name of the file (used as document ID)
            json_result: Result from Document Intelligence (optional)
            
        Returns:
            Dictionary containing extracted data or None if processing fails
        """
        pass