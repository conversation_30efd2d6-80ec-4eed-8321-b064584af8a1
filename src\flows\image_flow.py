import re
from src.flows.base_flow import BaseFlow
from src.data_processing import document_preprocessor, entity_extractor
from src.utils import data_transformer

from src.utils.logger import get_logger
LOGGER = get_logger("src.flows.image_flow")

class ImageFlow(BaseFlow):
    MARKDOWN_DELIMITER = "# Investment management entity name:"
    MARKDOWN_PATTERN = rf"{re.escape(MARKDOWN_DELIMITER)}.*?(?={re.escape(MARKDOWN_DELIMITER)}|\Z)"
    
    def __init__(self, llm_handler):
        super().__init__(llm_handler)

    async def async_process_document(self, file_url: str, file_name: str, json_result: dict = None):
        LOGGER.info(f"--- Starting original flow for: {file_name} ---")
        
        base64_images = await document_preprocessor.async_process_document_to_base64(file_url)
        if not base64_images:
            LOGGER.error(f"Could not convert document to base64 images for {file_name}.")
            return None
        
        try:
            total_tokens = 0
            pipeline_data = {}
            
            response = await entity_extractor.extract_investment_entities(self.llm_handler, base64_images)
            pipeline_data['investment_entities'] = response["result"]
            total_tokens += response.get("tokens", 0)
            LOGGER.info(f"Extracted {len(pipeline_data['investment_entities'].get('investment_management_entity', []))} investment entities.")
            
            response = await entity_extractor.remove_duplicate_entities(self.llm_handler, base64_images, pipeline_data['investment_entities'])
            pipeline_data['investment_entities'] = response["result"]
            total_tokens += response.get("tokens", 0)
            
            response = await entity_extractor.extract_dates(self.llm_handler, base64_images, pipeline_data['investment_entities'])
            pipeline_data['dates_entity'] = response["result"]
            total_tokens += response.get("tokens", 0)
            pipeline_data['investment_entities'] = entity_extractor.map_dates_to_entities(pipeline_data['dates_entity'], pipeline_data['investment_entities'])
            
            response = await entity_extractor.extract_ime_accounts(self.llm_handler, base64_images, pipeline_data['investment_entities'])
            pipeline_data['ime_accounts_entity'] = response["result"]
            total_tokens += response.get("tokens", 0)
            
            response = await entity_extractor.extract_plan_sponsors(self.llm_handler, base64_images, pipeline_data['investment_entities'], pipeline_data['ime_accounts_entity'])
            pipeline_data['plan_sponsor_entity'] = response["result"]
            total_tokens += response.get("tokens", 0)
            
            response = await entity_extractor.extract_ps_accounts(self.llm_handler, base64_images, pipeline_data['plan_sponsor_entity'])
            pipeline_data['pse_accounts_entity'] = response["result"]
            total_tokens += response.get("tokens", 0)
            
            if pipeline_data['plan_sponsor_entity'].get("plan_sponsor"):
                response = await entity_extractor.relate_ps_accounts_to_ime_accounts(self.llm_handler, base64_images, pipeline_data['ime_accounts_entity'], pipeline_data['pse_accounts_entity'])
                pipeline_data['pse_accounts_entity'] = response["result"]
                total_tokens += response.get("tokens", 0)
            
            holdings_markdown, tokens_from_step = await entity_extractor.extract_holdings_as_markdown(self.llm_handler, base64_images, **pipeline_data)
            total_tokens += tokens_from_step
            holding_chunks = re.findall(self.MARKDOWN_PATTERN, holdings_markdown, re.DOTALL)
            response = await entity_extractor.extract_holdings_from_markdown(self.llm_handler, holding_chunks)
            pipeline_data['holdings_entity'] = response["result"]
            total_tokens += response.get("tokens", 0)
            LOGGER.info(f"Extracted {len(pipeline_data['holdings_entity'].get('holding', []))} holdings.")
            
            transactions_markdown, tokens_from_step = await entity_extractor.extract_transactions_as_markdown(self.llm_handler, base64_images, **pipeline_data)
            total_tokens += tokens_from_step
            transaction_chunks = re.findall(self.MARKDOWN_PATTERN, transactions_markdown, re.DOTALL)
            response = await entity_extractor.extract_transactions_from_markdown(self.llm_handler, transaction_chunks)
            pipeline_data['transactions_entity'] = response["result"]
            total_tokens += response.get("tokens", 0)
            LOGGER.info(f"Extracted {len(pipeline_data['transactions_entity'].get('transaction', []))} transactions.")

            final_schema = {
                "date": pipeline_data.get('dates_entity', {}).get('date', []),
                "investment_management_entity": pipeline_data.get('investment_entities', {}).get('investment_management_entity', []),
                "plan_sponsor": pipeline_data.get('plan_sponsor_entity', {}).get('plan_sponsor', []),
                "ime_account": pipeline_data.get('ime_accounts_entity', {}).get('ime_account', []),
                "ps_account": pipeline_data.get('pse_accounts_entity', {}).get('ps_account', []),
                "holding": pipeline_data.get('holdings_entity', {}).get('holding', []),
                "transaction": pipeline_data.get('transactions_entity', {}).get('transaction', [])
            }
            
            data_transformer.reindex_list_ids(final_schema.get('holding', []), 'ht_id', 'HT')
            data_transformer.reindex_list_ids(final_schema.get('transaction', []), 'tt_id', 'TT')
            
            final_json = data_transformer.transform_json_structure(final_schema)
            final_json["document_id"] = file_name
            
            LOGGER.info(f"--- Successfully processed document with original flow: {file_name} ---")
            
            return {
                "file_url": file_url,
                "file_name": file_name,
                "result": final_json,
                "tokens": total_tokens 
            }
        except Exception as e:
            LOGGER.critical(f"A critical error occurred while processing {file_name} with original flow: {e}", exc_info=True)
            return None