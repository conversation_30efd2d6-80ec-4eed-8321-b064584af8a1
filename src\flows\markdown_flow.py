from src.flows.base_flow import BaseFlow
from src.markdown_flow.organize_ms_extraction import OrganizeMSExtraction
from src.markdown_flow.post_processing_llm import PostProcessingLLM

from src.utils.logger import get_logger
LOGGER = get_logger("src.flows.markdown_flow")

class MarkdownFlow(BaseFlow):
    """
    Alternative document processing flow that uses Document Intelligence and markdown processing.
    """
    
    def __init__(self, llm_handler, document_intelligence_client=None):
        """
        Initialize the alternative flow.
        
        Args:
            llm_handler: Handler for LLM interactions
            document_intelligence_client: Client for document intelligence (required for this flow)
        """
        super().__init__(llm_handler, document_intelligence_client)
        self.organize_ms_extraction = OrganizeMSExtraction()
        self.post_processing_llm = PostProcessingLLM(llm_handler)
        
    async def async_process_document(self, file_url: str, file_name: str, json_result: dict = None):
        """
        Process a document using the alternative flow.
        
        Args:
            file_url: URL of the document to process (not used directly in this flow)
            file_name: Name of the file (used as document ID)
            json_result: Result from Document Intelligence (required for this flow)
            
        Returns:
            Dictionary containing extracted data or None if processing fails
        """
        LOGGER.info(f"--- Starting alternative flow for: {file_name} ---")
        
        if not json_result:
            LOGGER.error(f"json_result is required for alternative flow but not provided for {file_name}")
            return None
        
        try:
            # Stage 1: Organize MS extraction into dataframe
            organized_extraction = self.organize_ms_extraction.run(json_result)
            LOGGER.info("MS extraction organized in dataframe")
            
            # Stage 2: Post-process with LLM
            specific_extraction, tokens_used = await self.post_processing_llm.run(organized_extraction)

            specific_extraction["document_id"] = file_name

            LOGGER.info(f"Successfully processed document with alternative flow: {file_name}")
            # LOGGER.info(f"Specific Extraction:\n{specific_extraction}")
            return {
                "file_url": file_url,
                "file_name": file_name,
                "result": specific_extraction,
                "tokens": tokens_used
            }
        except Exception as e:
            LOGGER.critical(f"A critical error occurred while processing {file_name} with alternative flow: {e}", exc_info=True)
            return None