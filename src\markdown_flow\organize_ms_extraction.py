import pandas as pd
import numpy as np
from src.utils.calculate_segments import calculate_segments
from itertools import groupby

from src.utils.logger import get_logger
LOGGER = get_logger("src.markdown_flow.organize_ms_extraction")

class OrganizeMSExtraction:
    """
    Class to organize extraction results from Microsoft Document Intelligence into a structured dataframe.
    """
    
    def __init__(self):
        """Initialize the organizer."""
        pass
    
    def run(self, extracted_json: dict) -> pd.DataFrame:
        """
        Run the organization process on the extracted JSON.
        
        Args:
            extracted_json: JSON extracted from Document Intelligence
            
        Returns:
            Dataframe with organized content
        """
        # Process paragraphs and tables
        paragraphs_dict = self.paragraphs_processing(extracted_json)
        tables_dict = self.tables_processing(extracted_json)
        
        # Convert to dataframes
        paragraphs_df = pd.DataFrame(paragraphs_dict).reset_index().rename(columns={"index": "PID"})
        tables_df = pd.DataFrame(tables_dict).reset_index().rename(columns={"index": "TID"})
        
        # Handle empty cases
        if tables_df.empty:
            final_df = paragraphs_df.drop(columns=["PID"])
        elif paragraphs_df.empty:
            final_df = tables_df.drop(columns=["PID"])
        else:
            final_df = self.solve_paragraphs_inside_tables(paragraphs_df, tables_df)
        
        # Sort the final dataframe
        df_sorted = self.sort(final_df, extracted_json)
        return df_sorted
    
    def solve_paragraphs_inside_tables(self, paragraphs_df: pd.DataFrame, tables_df: pd.DataFrame):
        """
        Handle paragraphs that are inside tables.
        
        Args:
            paragraphs_df: Dataframe with paragraphs
            tables_df: Dataframe with tables
            
        Returns:
            Combined dataframe with resolved overlaps
        """
        tables_df_1 = tables_df[["x0", "x1", "y0", "y1", "page"]].copy()
        tables_df_1["key"] = 1
        
        paragraphs_df_1 = paragraphs_df[["PID", "mid_x", "mid_y", "page"]].copy()
        paragraphs_df_1["key"] = 1
        
        # Merge dataframes
        p_t_merge = paragraphs_df_1.merge(tables_df_1, on=["key", "page"], how="outer")
        
        # Check if paragraph is inside table
        p_t_merge["comparison"] = ~(
            (p_t_merge["mid_x"] <= p_t_merge["x1"]) & (p_t_merge["mid_x"] >= p_t_merge["x0"]) &
            (p_t_merge["mid_y"] <= p_t_merge["y1"]) & (p_t_merge["mid_y"] >= p_t_merge["y0"]) 
        )
        
        # Get paragraphs that are not inside tables
        validation = p_t_merge.groupby("PID")["comparison"].prod()
        sole_paragraphs_df = paragraphs_df[validation == 1]
        
        # Combine tables and sole paragraphs
        df_contents = pd.concat([tables_df, sole_paragraphs_df], axis=0)
        return df_contents.drop(columns=["TID", "PID"])
    
    def tables_processing(self, extracted_json: dict):
        """
        Process tables from the extracted JSON.
        
        Args:
            extracted_json: JSON extracted from Document Intelligence
            
        Returns:
            List of processed table dictionaries
        """
        tables = extracted_json["tables"]
        table_results = []
        
        # Process each table
        for i, table in enumerate(tables):
            rows = table["rowCount"]
            cols = table["columnCount"]
            table_arr = np.full((rows, cols), "", dtype=object)
            column_header = None
            
            # Process each cell
            for cell in table["cells"]:
                cell_row = cell["rowIndex"]
                cell_col = cell["columnIndex"]
                row_span = cell.get("rowSpan", 1)
                col_span = cell.get("columnSpan", 1)
                column_header = cell_row if cell.get("kind") == "columnHeader" else column_header
                
                content = cell["content"].replace("\n", " ")
                
                # Add row/col span info
                if row_span > 1:
                    content = f'<td rowspan="{row_span}">{content}</td>'
                if col_span > 1:
                    content = f'<td colspan="{col_span}">{content}</td>'
                
                table_arr[cell_row][cell_col] = content
            
            # Convert to markdown table
            table_rows = [" | ".join(row) for row in table_arr]
            
            if column_header is not None:
                header_separator = "|".join(["--" for _ in range(cols)])
                table_rows.insert(column_header + 1, header_separator)
            
            table_rows = [f"| {row} |" for row in table_rows]
            table_str = "\n".join(table_rows)
            
            # Get bounding box
            if "boundingRegions" in table:
                page_number = cell["boundingRegions"][0]["pageNumber"]
                polygon_t = table["boundingRegions"][0]["polygon"]
                bboxes_x = [polygon_t[0], polygon_t[2], polygon_t[4], polygon_t[6]]
                bboxes_y = [polygon_t[1], polygon_t[3], polygon_t[5], polygon_t[7]]
                x1 = max(bboxes_x)
                y1 = max(bboxes_y)
                x0 = min(bboxes_x)
                y0 = min(bboxes_y)
                
                # Calculate midpoints
                mid_x = round((x0 + x1) / 2, 4)
                mid_y = round((y0 + y1) / 2, 4)
            else:
                # Default values if no bounding regions
                page_number = 1
                mid_x = None
                mid_y = None
                x0 = None
                y0 = None
                x1 = None
                y1 = None
            
            # Store processed table
            res = {
                "content": table_str,
                "type": "table",
                "mid_x": mid_x,
                "mid_y": mid_y,
                "x0": x0,
                "y0": y0,
                "x1": x1,
                "y1": y1,
                "page": page_number
            }
            table_results.append(res)
        
        return table_results
    
    def paragraphs_processing(self, extracted_json: dict):
        """
        Process paragraphs from the extracted JSON.
        
        Args:
            extracted_json: JSON extracted from Document Intelligence
            
        Returns:
            List of processed paragraph dictionaries
        """
        paragraphs = extracted_json.get("paragraphs", [])
        paragraphs_arr = []
        
        # Process each paragraph
        for paragraph in paragraphs:
            prefix = ""
            role = paragraph.get("role")
            
            # Skip page numbers
            if role == "pageNumber":
                continue
            
            # Add prefix based on role
            if role == "title":
                prefix = "# "
            elif role == "sectionHeading":
                prefix = "## "
            elif role == "pageHeader":
                prefix = ""  # This one is still to be analyzed if to add it or not
            
            # Get content and page number
            content = prefix + paragraph["content"]
            
            if "boundingRegions" in paragraph:
                page_number = paragraph["boundingRegions"][0]["pageNumber"]
                polygon = paragraph["boundingRegions"][0]["polygon"]
                
                # Get bounding box coordinates
                bboxes_x = [polygon[0], polygon[2], polygon[4], polygon[6]]
                bboxes_y = [polygon[1], polygon[3], polygon[5], polygon[7]]
                
                # Calculate bounding box
                x1 = max(bboxes_x)
                y1 = max(bboxes_y)
                x0 = min(bboxes_x)
                y0 = min(bboxes_y)
                
                # Calculate midpoints
                mid_x = round((x0 + x1) / 2, 4)
                mid_y = round((y0 + y1) / 2, 4)
            else:
                # Default values if no bounding regions
                page_number = 1
                mid_x = None
                mid_y = None
                x0 = None
                y0 = None
                x1 = None
                y1 = None
            
            # Store processed paragraph
            paragraphs_arr.append({
                "content": content,
                "type": "paragraph",
                "mid_x": mid_x,
                "mid_y": mid_y,
                "x0": x0,
                "y0": y0,
                "x1": x1,
                "y1": y1,
                "page": page_number
            })
        
        return paragraphs_arr
    
    def sort(self, df: pd.DataFrame, extracted_json: dict) -> pd.DataFrame:
        """
        Sort the dataframe based on document layout.
        
        Args:
            df: Dataframe to sort
            extracted_json: JSON extracted from Document Intelligence
            
        Returns:
            Sorted dataframe
        """
        # Extract page dimensions
        page_dimensions = [
            {"page": p["pageNumber"], "width": p["width"], "height": p["height"]}
            for p in extracted_json["pages"]
            if "pageNumber" in p and "width" in p and "height" in p
        ]
        
        if len(page_dimensions) == 0:
            LOGGER.warning("No page dimensions found in the extracted JSON.")
            return df
        
        df_page_dimensions = pd.DataFrame.from_records(page_dimensions)
        
        # Merge page dimensions with the input DataFrame
        df_2 = df.merge(df_page_dimensions, on="page", how="left")
        final_df = pd.DataFrame()
        
        # Process each page
        for page, data in df_2.groupby("page"):
            # Calculate normalized x-positions for columns
            x_pos_n = data["x0"] / data["width"]
            column_result = calculate_segments(x_pos_n.values, max_k=5)
            data["column"] = column_result["labels"]
            
            # Calculate normalized y-positions for rows
            y_pos_n = data["y1"] / data["height"]
            y_pos_rounded = [round(x, 2) for x in y_pos_n]
        
            # Assign incremental numbers to each group
            group_numbers = []
            group_id = 0
            for key, group in groupby(y_pos_rounded):
                group_numbers.extend([group_id] * len(list(group)))
                group_id += 1
            
            # Map group_numbers to ascending order based on y_pos_rounded
            unique_groups = sorted(set(group_numbers), key=lambda g: min([y for y, grp in zip(y_pos_rounded, group_numbers) if grp == g]))
            group_mapping = {old_label: new_label for new_label, old_label in enumerate(unique_groups)}
            sorted_labels = [group_mapping[label] for label in group_numbers]
            data["row"] = sorted_labels
            
            # Sort the data
            final_df = pd.concat([
                final_df,
                data.sort_values(by=["row", "column"], ascending=True)
            ])
        
        # Clean up the final DataFrame
        final_df.drop(columns=["width", "height", "row", "column"], inplace=True)
        final_df["content"] = final_df["content"].str.strip()
        final_df = final_df[final_df["content"].str.len() > 1]
        
        return final_df