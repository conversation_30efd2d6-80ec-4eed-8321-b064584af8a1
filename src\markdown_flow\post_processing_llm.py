import json
import pandas as pd
import re
from src.services.llm_handler import LLMHandler
from src.services.prompt_manager import Prompt<PERSON>anager

from src.utils.logger import get_logger
LOGGER = get_logger("src.markdown_flow.post_processing_llm")

class PostProcessingLLM:
    """
    Post-processing class for the alternative flow that uses LLM to extract structured data.
    """
    
    def __init__(self, llm_handler: LLMHandler):
        self.llm_handler = llm_handler
        self.prompt_manager = PromptManager(llm_handler.prompts_dir)
    
    async def run(self, df):
        total_tokens = 0
        final_result = {
            "document_id": None, "investment_management_entity": [], "date": [], "plan_sponsor": [],
            "ime_account": [], "ps_account": [], "holding": [], "transaction": []
        }
        
        markdown_output = self.format_in_markdown(df)
        
        ime_response = await self.get_ime(markdown_output)
        entities = ime_response["result"]
        total_tokens += ime_response.get("tokens", 0)
        final_result["investment_management_entity"] = entities
        LOGGER.info("Investment management entities extracted successfully")
        
        plan_sponsors, ime_accounts, dates_result = [], [], []
        skip_ps_ids = set()
        
        for entity in entities:
            if "response" in entity: entity = entity["response"]
            entity_name = entity.get("data", {}).get("ime_name", {}).get("value", "Unknown Entity")
            
            dates_response = await self.get_dates(markdown_output, entity)
            dates = dates_response["result"]
            total_tokens += dates_response.get("tokens", 0)
            dates_result.extend(dates)
            LOGGER.info(f"Dates for '{entity_name}' extracted successfully")
            
            accounts_response = await self.get_ime_account_type(markdown_output, entity)
            accounts = accounts_response["result"]
            total_tokens += accounts_response.get("tokens", 0)
            ime_accounts.extend(accounts)
            LOGGER.info(f"IME account for '{entity_name}' extracted successfully")
            
            if any(acc.get("margin_account") == "yes" for acc in accounts):
                LOGGER.info(f"Skipping plan sponsors and plans for '{entity['data']['ime_name']['value']}'")
                skip_ps_ids.add(entity.get("ime_id"))
                continue
            
            sponsors_response = await self.get_ps(markdown_output, entity)
            sponsors = sponsors_response["result"]
            total_tokens += sponsors_response.get("tokens", 0)
            plan_sponsors.extend(sponsors)
            LOGGER.info(f"Plan sponsor for '{entity_name}' extracted successfully")
        
        final_result["date"] = dates_result
        final_result["ime_account"] = ime_accounts
        final_result["plan_sponsor"] = plan_sponsors
        
        ps_plans = []
        for ps in plan_sponsors:
            ps_name = ps.get("data", {}).get("ps_name", {}).get("value", "Unknown Sponsor")
            if ps.get("ime_id") in skip_ps_ids:
                LOGGER.info(f"Skipping plans for plan sponsor '{ps_name}'")
                continue
            
            plans_response = await self.get_pse_plan(markdown_output, ps)
            plans = plans_response["result"]
            total_tokens += plans_response.get("tokens", 0)
            ps_plans.extend(plans)
            LOGGER.info(f"Plans for '{ps_name}' extracted successfully")
        
        LOGGER.info("Starting IME account to PS account mapping...")
        mapping_response = await self.get_ps_imeacc_mapping(plan_sponsors, ime_accounts, ps_plans)
        ps_plans = mapping_response["result"]
        total_tokens += mapping_response.get("tokens", 0)
        LOGGER.info("Successfully mapped PS accounts with IME accounts")
        final_result["ps_account"] = ps_plans
        
        connections_response = await self.join_ime_ps(markdown_output, entities, plan_sponsors, ime_accounts, ps_plans)
        connections = connections_response["result"]
        total_tokens += connections_response.get("tokens", 0)
        
        chunks = self.chunk_text_sliding_window(markdown_output)
        
        improve_holdings_response = await self.improve_text(connections, chunks, prompt="improve_holdings_chunks_prompt")
        improved_holdings_text = improve_holdings_response["result"]
        total_tokens += improve_holdings_response.get("tokens", 0)
        
        improve_transactions_response = await self.improve_text(connections, chunks, prompt="improve_transactions_chunks_prompt")
        improved_transactions_text = improve_transactions_response["result"]
        total_tokens += improve_transactions_response.get("tokens", 0)
        
        holdings_response = await self.process_holdings(improved_holdings_text, connections)
        holdings = holdings_response["result"]
        total_tokens += holdings_response.get("tokens", 0)
        final_result["holding"] = holdings
        LOGGER.info(f"{len(holdings)} Holdings extracted successfully")
        
        transactions_response = await self.process_transactions(improved_transactions_text, connections)
        transactions = transactions_response["result"]
        total_tokens += transactions_response.get("tokens", 0)
        final_result["transaction"] = transactions
        LOGGER.info(f"{len(transactions)} Transactions extracted successfully")

        final_result = self.replace_placeholder(final_result)
        final_result = self.create_final_json(final_result)
        
        return final_result, total_tokens
    
    def all_fields_null(self, obj):
        if isinstance(obj, dict):
            if "value" in obj: return obj["value"] is None or obj["value"] == ""
            return all(self.all_fields_null(v) for v in obj.values())
        elif isinstance(obj, list):
            return all(self.all_fields_null(item) for item in obj)
        return True

    async def _call_llm_for_json(self, template_name: str, context: dict, default_response: dict = None) -> dict:
        prompt = self.prompt_manager.get_prompt(template_name, **context)
        raw_response, tokens = await self.llm_handler._call_api(prompt, json_mode=True)
        
        if not raw_response:
            return {"result": default_response or {}, "tokens": tokens or 0}
        
        try:
            result = json.loads(raw_response)
            return {"result": result, "tokens": tokens or 0}
        except (json.JSONDecodeError, TypeError):
            LOGGER.error(f"Failed to decode JSON from LLM for prompt {template_name}.")
            return {"result": default_response or {}, "tokens": tokens or 0}

    async def get_ime(self, text: str) -> dict:
        total_tokens_step = 0
        response_data = await self._call_llm_for_json("investment_management_prompt", {"statement_text": text})
        ime_result = response_data["result"]
        total_tokens_step += response_data.get("tokens", 0)
        entities = ime_result.get("investment_management_entities", [])

        def has_valid_name(entity):
            return entity.get("data", {}).get("ime_name", {}).get("value")

        if not entities or not any(has_valid_name(e) for e in entities):
            LOGGER.warning("Primary IME extraction failed. Trying fallback.")
            response_data = await self._call_llm_for_json("ime_elefante", {"statement_text": text})
            ime_result = response_data["result"]
            total_tokens_step += response_data.get("tokens", 0)
            entities = ime_result.get("investment_management_entities", [])

        for idx, entity in enumerate(entities, start=1):
            entity["ime_id"] = f"IME{idx:03d}"

        if not entities:
            LOGGER.warning("All extraction steps failed. Creating a placeholder.")
            entities = [{"ime_id": "IME001", "data": {"ime_name": {"value": "ELEFANTE ROSA"}}}]
        
        return {"result": entities, "tokens": total_tokens_step}

    async def get_dates(self, text: str, entity: dict) -> dict:
        ime_name_value = entity.get("data", {}).get("ime_name", {}).get("value", "")
        context = {"statement_text": text, "ime_id": entity.get("ime_id", ""), "ime_name": ime_name_value}
        response = await self._call_llm_for_json("dates_prompt", context)
        
        dates_list = response["result"].get("date", [])
        for idx, date in enumerate(dates_list, start=1):
            date["id"] = f"DATE{idx:03d}"
        
        response["result"] = dates_list
        return response
    
    async def get_ime_account_type(self, text: str, entity: dict) -> dict:
        ime_name_value = entity.get("data", {}).get("ime_name", {}).get("value", "")
        context = {"statement_text": text, "ime_id": entity.get("ime_id", ""), "ime_name": ime_name_value}
        response = await self._call_llm_for_json("investment_management_account_type_prompt", context)
        
        accounts = response["result"].get("accounts", [])
        for idx, acc in enumerate(accounts, start=1):
            if "data" in acc and "number" in acc["data"]:
                acc["data"]["ime_acc_number"] = acc["data"].pop("number")
            acc["ime_acc_id"] = f"IMEACC{idx:03d}"
            acc.pop("descriptor", None)
        
        if len(accounts) == 1 and self.all_fields_null(accounts[0]):
            accounts = []
        
        response["result"] = accounts
        return response

    async def get_pse_plan(self, text: str, ps: dict) -> dict:
        ime_name = ps.get("data", {}).get("ime_name", {}).get("value", "")
        ps_name = ps.get("data", {}).get("ps_name", {}).get("value", "")
        entity_details = f'\n- Investment Management Entity: {ime_name}\n- Plan Sponsor ID: {ps.get("ps_id", "")}\n- Plan Sponsor Name: {ps_name}'
        context = {"statement_text": text, "entity_details": entity_details}
        response = await self._call_llm_for_json("plan_sponsor_plan_prompt", context)
        
        plans = response["result"].get("ps_accounts", [])
        for idx, plan in enumerate(plans, start=1):
            plan["id"] = f"PSACC{idx:03d}"
            plan.pop("descriptor", None)
        
        if len(plans) == 1 and self.all_fields_null(plans[0]):
            plans = []
            
        response["result"] = plans
        return response

    async def get_ps(self, text: str, entity: dict) -> dict:
        context = {"statement_text": text, "entity_details": f"{entity.get('ime_id', '')} {entity.get('descriptor', '')}"}
        response = await self._call_llm_for_json("plan_sponsor_prompt", context)
        
        ps_list = response["result"].get("plan_sponsor_entities", [])
        if len(ps_list) == 1 and self.all_fields_null(ps_list[0]):
            ps_list = []
        
        for idx, ps in enumerate(ps_list, start=1):
            ps["ps_id"] = f"PS{idx:03d}"
        
        response["result"] = ps_list
        return response

    async def join_ime_ps(self, text: str, entities: list, plan_sponsors: list, ime_accounts: list, ps_plans: list) -> dict:
        ime_acc_str = ""
        if ime_accounts:
            entity_name = entities[0].get("data", {}).get("ime_name", {}).get("value", "") if entities else ""
            for ime_acc in ime_accounts:
                ime_acc_str += f"\n- [ime_id: {ime_acc['ime_id']}] {entity_name} | Account: [ime_acc_id: {ime_acc['ime_acc_id']}] {ime_acc['data']['ime_acc_name']['value']} | Account number: {ime_acc['data']['ime_acc_number']['value']}"
        else:
            for entity in entities:
                ime_acc_str += f"\n- [ime_id: {entity['ime_id']}] {entity['data']['ime_name']['value']}"
        
        ps_str = ""
        if ps_plans:
            for ps_plan in ps_plans:
                ps_name = next((ps.get("data", {}).get("ps_name", {}).get("value", "") for ps in plan_sponsors if ps.get('ps_id') == ps_plan.get('ps_id')), '')
                plan_name = ps_plan.get("data", {}).get("ps_plan_name", {}).get("value", "")
                plan_number = ps_plan.get("data", {}).get("number", {}).get("value", "")
                ps_str += f"\n- [ps_id: {ps_plan.get('ps_id', '')}] {ps_name} | Plan id: [id: {ps_plan.get('id', '')}] {plan_name} | Plan number: {plan_number}"
        else:
            for ps in plan_sponsors:
                ps_name = ps.get("data", {}).get("ps_name", {}).get("value", "")
                ps_str += f"\n- [ps_id: {ps.get('ps_id', '')}] {ps_name}"

        context = {"statement_text": text, "ime_acc": ime_acc_str, "ps_plan": ps_str}
        return await self._call_llm_for_json("join_ime_ps", context)

    async def process_holdings(self, improved_text: str, connections: dict) -> dict:
        total_tokens_step = 0
        connections_list = connections.get("connections", [])
        if not isinstance(connections_list, list):
            return {"result": [], "tokens": 0}
        
        holdings = []
        holding_counter = 1
        
        for conn in connections_list:
            ime = conn.get("ime") or {}
            ime_acc = conn.get("ime_acc") or {}
            ps = conn.get("ps") or {}
            ps_plan = conn.get("ps_plan") or {}
            entity_details = f"IME ID: {ime.get('ime_id')} | IME Name: {ime.get('ime_name')} | IME Acc ID {ime_acc.get('ime_acc_id')} | PS ID {ps.get('ps_id')} | PS Plan ID: {ps_plan.get('ps_plan_id')}"

            try:
                context = {"entity_details": entity_details, "improved_texts_full": improved_text}
                response = await self._call_llm_for_json("holdings_prompt", context)
                total_tokens_step += response.get("tokens", 0)
                
                for holding in response["result"].get("holdings", []):
                    holding.update({
                        "ime_id": ime.get("ime_id"), "ime_acc_id": ime_acc.get("ime_acc_id"),
                        "ps_id": ps.get("ps_id"), "ps_plan_id": ps_plan.get("ps_plan_id"),
                        "h_id": f"H{holding_counter:03d}"
                    })
                    holding_counter += 1
                    holdings.append(holding)
            except Exception as e:
                LOGGER.error(f"Error extracting holdings for {ime.get('ime_id')}: {e}")
                continue
        
        return {"result": holdings, "tokens": total_tokens_step}

    async def process_transactions(self, improved_text: str, connections: dict) -> dict:
        total_tokens_step = 0
        connections_list = connections.get("connections", [])
        if not isinstance(connections_list, list):
            return {"result": [], "tokens": 0}
        
        transactions = []
        counter = 1
        
        for conn in connections_list:
            ime = conn.get("ime") or {}
            ime_acc = conn.get("ime_acc") or {}
            ps = conn.get("ps") or {}
            ps_plan = conn.get("ps_plan") or {}
            entity_details = f"IME ID: {ime.get('ime_id')} | IME Name: {ime.get('ime_name')} | IME Acc ID {ime_acc.get('ime_acc_id')} | PS ID {ps.get('ps_id')} | PS Plan ID: {ps_plan.get('ps_plan_id')}"

            try:
                context = {"entity_details": entity_details, "improved_texts_full": improved_text}
                response = await self._call_llm_for_json("transactions_prompt", context)
                total_tokens_step += response.get("tokens", 0)
                
                for transaction in response["result"].get("transactions", []):
                    transaction.update({
                        "ime_id": ime.get("ime_id"), "ime_acc_id": ime_acc.get("ime_acc_id"),
                        "ps_id": ps.get("ps_id"), "ps_plan_id": ps_plan.get("ps_plan_id"),
                        "t_id": f"T{counter:03d}"
                    })
                    counter += 1
                    transactions.append(transaction)
            except Exception as e:
                LOGGER.error(f"Error extracting transactions for {ime.get('ime_id')}: {e}")
                continue
        
        return {"result": transactions, "tokens": total_tokens_step}

    def chunk_text_sliding_window(self, text: str, chunk_length: int = 10000, chunk_overlap: int = 1000) -> list[str]:
        if not text: return []
        if len(text) <= chunk_length: return [text]
        chunks = []
        start_index = 0
        step = chunk_length - chunk_overlap
        while start_index < len(text):
            end_index = min(start_index + chunk_length, len(text))
            chunks.append(text[start_index:end_index])
            if end_index == len(text): break
            start_index += step
        return chunks

    async def improve_text(self, connections: dict, chunks: list[str], prompt: str) -> dict:
        total_tokens_step = 0
        connections_list = connections.get("connections", [])
        if not isinstance(connections_list, list):
            return {"result": "", "tokens": 0}
        
        entities_list = []
        for conn in connections_list:
            ime_name = (conn.get("ime") or {}).get("ime_name", "N/A")
            ime_acc_name = (conn.get("ime_acc") or {}).get("ime_acc_name", "N/A")
            ps_name = (conn.get("ps") or {}).get("ps_name", "N/A")
            ps_plan_name = (conn.get("ps_plan") or {}).get("ps_plan_name", "N/A")
            entities_list.append(f"- IME: {ime_name} | IME Acc: {ime_acc_name} | PS: {ps_name} | PS Plan: {ps_plan_name}")
        entities_str = "\n".join(entities_list)
        
        improved_texts = []
        for i, chunk in enumerate(chunks):
            prompt_text = self.prompt_manager.get_prompt(prompt, chunk=chunk, entities_and_accounts=entities_str)
            response_text, tokens = await self.llm_handler.get_text_response(prompt_text)
            total_tokens_step += tokens
            
            response_2 = f"<START CHUNK {i+1}>\n{response_text or ''}\n<END CHUNK {i+1}>"
            improved_texts.append(response_2)
        
        improved_texts_full = "\n\n".join(improved_texts)
        return {"result": improved_texts_full, "tokens": total_tokens_step}

    async def get_ps_imeacc_mapping(self, plan_sponsors: list, ime_accounts: list, ps_accounts: list) -> dict:
        context = {
            "ime_accounts": json.dumps(ime_accounts, indent=2),
            "plan_sponsors": json.dumps(plan_sponsors, indent=2),
            "ps_accounts": json.dumps(ps_accounts, indent=2)
        }
        response = await self._call_llm_for_json("imeacc_id_prompt", context)
        mappings = response["result"].get("account_mappings", [])
        
        validated_ps_accounts = self.validate_and_apply_mappings(ime_accounts, ps_accounts, mappings)
        
        response["result"] = validated_ps_accounts
        return response
    
    def format_in_markdown(self, df: pd.DataFrame):
        """
        Format dataframe content as markdown.
        
        Args:
            df: Dataframe with document content
            
        Returns:
            Formatted markdown string
        """
        markdown_lines = []
        
        if df.empty:
            return ""
        
        for page, group in df.groupby("page"):
            markdown_lines.append(f"\n\n### Page {page}\n")
            
            for _, row in group.iterrows():
                content = row['content']
                x0 = row['x0']
                y0 = row['y0']
                x1 = row['x1']
                y1 = row['y1']
                
                if row["type"] == "paragraph":
                    markdown_lines.append(f"\n{content}")
                elif row["type"] == "table":
                    markdown_lines.append("\n#### TABLE START")
                    markdown_lines.append(content)
                    markdown_lines.append("#### TABLE END\n")
        
        # Join all lines into a single string
        markdown_output = "\n".join(markdown_lines)
        return markdown_output
    
    def replace_placeholder(self, final_result: dict) -> dict:
        """
        Replace placeholder values in the result.
        
        Args:
            final_result: Result dictionary to process
            
        Returns:
            Processed result dictionary
        """
        # Replace in investment_management_entities
        for entity in final_result.get("investment_management_entity", []):
            if entity.get("data", {}).get("ime_name", {}).get("value") == "ELEFANTE ROSA":
                entity["data"]["ime_name"]["value"] = None
        
        # Replace in date
        for date in final_result.get("date", []):
            if date.get("ime_name") == "ELEFANTE ROSA":
                date["ime_name"] = None
        
        # Replace in ime_accounts
        for acc in final_result.get("ime_account", []):
            if acc.get("data", {}).get("ime_acc_name", {}).get("value") == "ELEFANTE ROSA":
                acc["data"]["ime_acc_name"]["value"] = None
        
        # Replace in plan_sponsors
        for ps in final_result.get("plan_sponsor", []):
            if ps.get("data", {}).get("ps_name", {}).get("value") == "ELEFANTE ROSA":
                ps["data"]["ps_name"]["value"] = None
        
        # Replace in ps_accounts
        for psa in final_result.get("ps_account", []):
            if psa.get("data", {}).get("ps_plan_name", {}).get("value") == "ELEFANTE ROSA":
                psa["data"]["ps_plan_name"]["value"] = None
        
        # Replace in holdings
        for h in final_result.get("holding", []):
            if h.get("data", {}).get("name", {}).get("value") == "ELEFANTE ROSA":
                h["data"]["name"]["value"] = None
        
        # Replace in transactions
        for t in final_result.get("transaction", []):
            if t.get("data", {}).get("name", {}).get("value") == "ELEFANTE ROSA":
                t["data"]["name"]["value"] = None
        
        return final_result
    
    def validate_and_apply_mappings(self, ime_accounts, ps_plans, mappings):
        """
        Validate and apply mappings between IME accounts and PS plans.
        
        Args:
            ime_accounts: List of IME accounts
            ps_plans: List of PS plans
            mappings: Mappings to apply
            
        Returns:
            Updated PS plans with applied mappings
        """
        # Get valid IME account IDs
        valid_ime_acc_ids = {acc["ime_acc_id"] for acc in ime_accounts}
        LOGGER.info(f"Valid IME account IDs: {valid_ime_acc_ids}")
        
        # Create validation dictionary
        validated_mapping_dict = {}
        for m in mappings:
            if m.get("imeacc_id") in valid_ime_acc_ids:
                key = (m.get("id"), m.get("ps_id")) 
                validated_mapping_dict[key] = m.get("imeacc_id")
        
        # Apply mappings
        for plan in ps_plans:
            key = (plan.get("id"), plan.get("ps_id"))
            plan["imeacc_id"] = validated_mapping_dict.get(key, "")
            
            if plan["imeacc_id"]:
                LOGGER.info(f"Successfully mapped plan {key} to {plan['imeacc_id']}")
            else:
                LOGGER.warning(f"No mapping found for plan {key}")
        
        return ps_plans
    
    async def get_ps_imeacc_mapping(self, plan_sponsors: list, ime_accounts: list, ps_accounts: list) -> dict:
        """
        Get mappings between PS accounts and IME accounts.
        """
        context = {
            "ime_accounts": json.dumps(ime_accounts, indent=2),
            "plan_sponsors": json.dumps(plan_sponsors, indent=2),
            "ps_accounts": json.dumps(ps_accounts, indent=2)
        }
        # 1. Llama al LLM usando la función auxiliar que devuelve {result, tokens}
        response = await self._call_llm_for_json("imeacc_id_prompt", context)
        mappings = response["result"].get("account_mappings", [])
        
        # 2. Valida los mapeos como antes
        validated_ps_accounts = self.validate_and_apply_mappings(ime_accounts, ps_accounts, mappings)
        
        # 3. Pone la lista validada DENTRO del diccionario de respuesta y lo devuelve
        response["result"] = validated_ps_accounts
        return response # <--- DEVUELVE UN DICCIONARIO
    
    def create_final_json(self, data: dict) -> dict:
        """
        Create the final JSON structure.
        
        Args:
            data: Input data to structure
            
        Returns:
            Structured JSON data
        """
        final_json = {
            "document_id": data.get("document_id", ""),
            "investment_management_entity": [
                {
                    "id": self.__format_ids(entity.get("ime_id", "")),
                    "data": {
                        "name": {
                            "value": entity.get("data", {}).get("ime_name", {}).get("value", ""),
                            "page_number": entity.get("data", {}).get("ime_name", {}).get("page_number", None),
                        },
                        "address": {
                            "value": entity.get("data", {}).get("address", {}).get("value", ""),
                            "page_number": entity.get("data", {}).get("address", {}).get("page_number", None),
                        },
                        "website": {
                            "value": entity.get("data", {}).get("website", {}).get("value", ""),
                            "page_number": entity.get("data", {}).get("website", {}).get("page_number", None),
                        },
                        "entity_unique_id": {
                            "value": entity.get("data", {}).get("entity_unique_id", {}).get("value", ""),
                            "page_number": entity.get("data", {}).get("entity_unique_id", {}).get("page_number", None),
                        }
                    }
                } for entity in data.get("investment_management_entity", [])
            ],
            "date": [
                {
                    "id": self.__format_ids(date.get("id", "")),
                    "ime_id": self.__format_ids(date.get("ime_id", "")),
                    "data": {
                        "start_date": {
                            "value": date.get("data", {}).get("start_date", {}).get("value", ""),
                            "page_number": date.get("data", {}).get("start_date", {}).get("page_number", None),
                        },
                        "end_date": {
                            "value": date.get("data", {}).get("end_date", {}).get("value", ""),
                            "page_number": date.get("data", {}).get("end_date", {}).get("page_number", None),
                        }
                    }
                } for date in data.get("date", [])
            ],
            "plan_sponsor": [
                {
                    "id": self.__format_ids(ps.get("ps_id", "")),
                    "ime_id": self.__format_ids(ps.get("ime_id", "")),
                    "data": {
                        "name": {
                            "value": ps.get("data", {}).get("ps_name", {}).get("value", ""),
                            "page_number": ps.get("data", {}).get("ps_name", {}).get("page_number", None),
                        },
                        "address": {
                            "value": ps.get("data", {}).get("address", {}).get("value", ""),
                            "page_number": ps.get("data", {}).get("address", {}).get("page_number", None),
                        },
                        "website": {
                            "value": ps.get("data", {}).get("website", {}).get("value", ""),
                            "page_number": ps.get("data", {}).get("website", {}).get("page_number", None),
                        },
                        "entity_unique_id": {
                            "value": ps.get("data", {}).get("entity_unique_id", {}).get("value", ""),
                            "page_number": ps.get("data", {}).get("entity_unique_id", {}).get("page_number", None),
                        }
                    }
                } for ps in data.get("plan_sponsor", [])
            ],
            "ime_account": [
                {
                    "id": self.__format_ids(ime_acc.get("ime_acc_id", "")),
                    "ime_id": self.__format_ids(ime_acc.get("ime_id", "")),
                    "margin_account": ime_acc.get("margin_account", ""),
                    "data": {
                        "name": {
                            "value": ime_acc.get("data", {}).get("ime_acc_name", {}).get("value", ""),
                            "page_number": ime_acc.get("data", {}).get("ime_acc_name", {}).get("page_number", None),
                        },
                        "number": {
                            "value": ime_acc.get("data", {}).get("ime_acc_number", {}).get("value", ""),
                            "page_number": ime_acc.get("data", {}).get("ime_acc_number", {}).get("page_number", None),
                        }
                    }
                } for ime_acc in data.get("ime_account", [])
            ],
            "ps_account": [
                {
                    "id": self.__format_ids(ps_acc.get("id", "")),
                    "imeacc_id": self.__format_ids(ps_acc.get("imeacc_id", "")),
                    "ps_id": self.__format_ids(ps_acc.get("ps_id", "")),
                    "data": {
                        "name": {
                            "value": ps_acc.get("data", {}).get("ps_plan_name", {}).get("value", ""),
                            "page_number": ps_acc.get("data", {}).get("ps_plan_name", {}).get("page_number", None),
                        },
                        "number": {
                            "value": ps_acc.get("data", {}).get("number", {}).get("value", ""),
                            "page_number": ps_acc.get("data", {}).get("number", {}).get("page_number", None),
                        }
                    }
                } for ps_acc in data.get("ps_account", [])
            ],
            "holding": [
                {
                    "id": self.__format_ids(holding.get("h_id", "")),
                    "ime_id": self.__format_ids(holding.get("ime_id", "")),
                    "imeacc_id": self.__format_ids(holding.get("ime_acc_id", "")),
                    "ps_id": self.__format_ids(holding.get("ps_id", "")),
                    "psacc_id": self.__format_ids(holding.get("ps_plan_id", "")),
                    "ht_id": self.__format_ids(holding.get("ht_id", "")),
                    "data": {
                        "name": {
                            "value": holding.get("data", {}).get("name", {}).get("value", ""),
                            "page_number": holding.get("data", {}).get("name", {}).get("page_number", None),
                        },
                        "ticker": {
                            "value": holding.get("data", {}).get("ticker", {}).get("value", ""),
                            "page_number": holding.get("data", {}).get("ticker", {}).get("page_number", None),
                        },
                        "class": {
                            "value": holding.get("data", {}).get("class", {}).get("value", ""),
                            "page_number": holding.get("data", {}).get("class", {}).get("page_number", None),
                        },
                        "type": {
                            "value": holding.get("data", {}).get("type", {}).get("value", ""),
                            "page_number": holding.get("data", {}).get("type", {}).get("page_number", None),
                        },
                        "exchange": {
                            "value": holding.get("data", {}).get("exchange", {}).get("value", ""),
                            "page_number": holding.get("data", {}).get("exchange", {}).get("page_number", None),
                        },
                        "currency": {
                            "value": holding.get("data", {}).get("currency", {}).get("value", ""),
                            "page_number": holding.get("data", {}).get("currency", {}).get("page_number", None)
                        },
                        "beginning_value": {
                            "value": holding.get("data", {}).get("beginning_value", {}).get("value", ""),
                            "page_number": holding.get("data", {}).get("beginning_value", {}).get("page_number", None),
                        },
                        "ending_value": {
                            "value": holding.get("data", {}).get("ending_value", {}).get("value", ""),
                            "page_number": holding.get("data", {}).get("ending_value", {}).get("page_number", None),
                        },
                        "security_unique_id": {
                            "value": holding.get("data", {}).get("security_unique_id", {}).get("value", ""),
                            "page_number": holding.get("data", {}).get("security_unique_id", {}).get("page_number", None),
                        }
                    }
                } for holding in data.get("holding", [])
            ],
            "transaction": [
                {
                    "id": self.__format_ids(txn.get("t_id", "")),
                    "ime_id": self.__format_ids(txn.get("ime_id", "")),
                    "imeacc_id": self.__format_ids(txn.get("ime_acc_id", "")),
                    "ps_id": self.__format_ids(txn.get("ps_id", "")),
                    "psacc_id": self.__format_ids(txn.get("ps_plan_id", "")),
                    "tt_id": self.__format_ids(txn.get("tt_id", "")),
                    "data": {
                        "name": {
                            "value": txn.get("data", {}).get("name", {}).get("value", ""),
                            "page_number": txn.get("data", {}).get("name", {}).get("page_number", None),
                        },
                        "transaction_date": {
                            "value": txn.get("data", {}).get("transaction_date", {}).get("value", ""),
                            "page_number": txn.get("data", {}).get("transaction_date", {}).get("page_number", None),
                        },
                        "transaction_type": {
                            "value": txn.get("data", {}).get("transaction_type", {}).get("value", ""),
                            "page_number": txn.get("data", {}).get("transaction_type", {}).get("page_number", None),
                        },
                        "ticker": {
                            "value": txn.get("data", {}).get("ticker", {}).get("value", ""),
                            "page_number": txn.get("data", {}).get("ticker", {}).get("page_number", None),
                        },
                        "class": {
                            "value": txn.get("data", {}).get("class", {}).get("value", ""),
                            "page_number": txn.get("data", {}).get("class", {}).get("page_number", None),
                        },
                        "type": {
                            "value": txn.get("data", {}).get("type", {}).get("value", ""),
                            "page_number": txn.get("data", {}).get("type", {}).get("page_number", None),
                        },
                        "exchange": {
                            "value": txn.get("data", {}).get("exchange", {}).get("value", ""),
                            "page_number": txn.get("data", {}).get("exchange", {}).get("page_number", None),
                        },
                        "currency": {
                            "value": txn.get("data", {}).get("currency", {}).get("value", ""),
                            "page_number": txn.get("data", {}).get("currency", {}).get("page_number", None)
                        },
                        "investment_value": {
                            "value": txn.get("data", {}).get("investment_value", {}).get("value", ""),
                            "page_number": txn.get("data", {}).get("investment_value", {}).get("page_number", None),
                        },
                        "security_unique_id": {
                            "value": txn.get("data", {}).get("security_unique_id", {}).get("value", ""),
                            "page_number": txn.get("data", {}).get("security_unique_id", {}).get("page_number", None),
                        }
                    }
                } for txn in data.get("transaction", [])
            ],
        }
        return final_json
    
    def __format_ids(self, id_str: str):
        """
        Format IDs to match the required pattern.
        
        Args:
            id_str: ID string to format
            
        Returns:
            Formatted ID string
        """
        if not id_str or not isinstance(id_str, str) or len(id_str) < 2:
            return id_str
        
        # Extract prefix and numeric parts
        p1 = re.findall(r'^[a-zA-Z]+', id_str)
        p1 = p1[0] if p1 else ""
        p2 = re.findall(r'\d+$', id_str)
        p2 = p2[0] if p2 else ""
        
        return f"{p1}_{p2}" if p1 and p2 else id_str