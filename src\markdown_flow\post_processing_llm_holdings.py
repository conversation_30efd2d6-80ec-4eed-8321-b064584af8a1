import json
import pandas as pd
import re
from src.services.llm_handler import LLMHandler
from src.services.prompt_manager import Prompt<PERSON>anager

from src.utils.logger import get_logger
LOGGER = get_logger("src.markdown_flow.post_processing_llm")

class PostProcessingLLM:
    """
    Post-processing class for the alternative flow that uses LLM to extract structured data.
    """
    
    def __init__(self, llm_handler: LLMHandler):
        """
        Initialize the post-processing LLM handler.
        
        Args:
            llm_handler: Handler for LLM interactions
        """
        self.llm_handler = llm_handler

        self.prompt_manager = PromptManager(llm_handler.prompts_dir)
    
    async def run(self, df):
        """
        Run the post-processing pipeline on the extracted dataframe.
        
        Args:
            df: Dataframe with extracted document content
            
        Returns:
            Dictionary with structured extracted data
        """
        final_result = {
            "document_id": None, 
            "investment_management_entity": [],
            "date": [],
            "plan_sponsor": [],
            "ime_account": [],
            "ps_account": [],
            "holding": [],
            "transaction": []
        }
        
        # Format content as markdown
        markdown_output = self.format_in_markdown(df)
        
        # Extract investment management entities
        entities = await self.get_ime(markdown_output)
        final_result["investment_management_entity"] = entities
        LOGGER.info("Investment management entities extracted successfully")
        
        plan_sponsors = []
        ime_accounts = []
        dates_result = []
        skip_ps_ids = set()  # IDs to skip for plan sponsors
        
        # Process each entity
        for entity in entities:
            if "response" in entity:
                entity = entity["response"]
            
            # Extract dates
            dates = await self.get_dates(markdown_output, entity)
            dates_result.extend(dates)
            LOGGER.info(f"Dates for '{entity['data']['ime_name']['value']}' extracted successfully")
            
            # Extract IME account type
            accounts = await self.get_ime_account_type(markdown_output, entity)
            ime_accounts.extend(accounts)
            LOGGER.info(f"IME account for '{entity['data']['ime_name']['value']}' extracted successfully")
            
            # Skip plan sponsors if margin account exists
            if any(acc.get("margin_account") == "yes" for acc in accounts):
                LOGGER.info(f"Skipping plan sponsors and plans for '{entity['data']['ime_name']['value']}'")
                skip_ps_ids.add(entity.get("ime_id"))
                continue
            
            # Extract plan sponsors
            sponsors = await self.get_ps(markdown_output, entity)
            plan_sponsors.extend(sponsors)
            LOGGER.info(f"Plan sponsor for '{entity['data']['ime_name']['value']}' entities extracted successfully")
        
        final_result["date"] = dates_result
        final_result["ime_account"] = ime_accounts
        final_result["plan_sponsor"] = plan_sponsors
        
        ps_plans = []
        for ps in plan_sponsors:
            # Skip if marked for skipping
            if ps.get("ime_id") in skip_ps_ids:
                LOGGER.info(f"Skipping plans for plan sponsor '{ps['data']['ps_name']['value']}'")
                continue
            
            plans = await self.get_pse_plan(markdown_output, ps)
            ps_plans.extend(plans)
            LOGGER.info(f"Plans for '{ps['data']['ps_name']['value']}' extracted successfully")
        
        # Map PS accounts to IME accounts
        LOGGER.info("Starting IME account to PS account mapping...")
        ps_plans = await self.get_ps_imeacc_mapping(plan_sponsors, ime_accounts, ps_plans)
        LOGGER.info("Successfully mapped PS accounts with IME accounts")
        final_result["ps_account"] = ps_plans
        
        # Join entities and plan sponsors
        connections = await self.join_ime_ps(markdown_output, entities, plan_sponsors, ime_accounts, ps_plans)
        
        # Chunk and improve texts
        chunks = self.chunk_text_sliding_window(markdown_output)
        improved_holdings_text = await self.improve_text(connections, chunks, prompt="improve_holdings_chunks_prompt")
        improved_transactions_text = await self.improve_text(connections, chunks, prompt="improve_transactions_chunks_prompt")
        
        # Extract holdings
        holdings = await self.process_holdings(improved_holdings_text, connections)
        final_result["holding"] = holdings
        LOGGER.info(f"{len(holdings)} Holdings extracted successfully")
        
        # Extract transactions
        transactions = await self.process_transactions(improved_transactions_text, connections)
        final_result["transaction"] = transactions
        LOGGER.info(f"{len(transactions)} Transactions extracted successfully")

        final_result = self.replace_placeholder(final_result)
        final_result = self.create_final_json(final_result)
        
        return final_result
    
    def all_fields_null(self, obj):
        """
        Check if all fields in an object are null or empty.
        
        Args:
            obj: Object to check
            
        Returns:
            True if all fields are null or empty, False otherwise
        """
        if isinstance(obj, dict):
            if "value" in obj:
                return obj["value"] is None or obj["value"] == ""
            return all(self.all_fields_null(v) for v in obj.values())
        elif isinstance(obj, list):
            return all(self.all_fields_null(item) for item in obj)
        else:
            return True
    
    async def get_ime(self, text: str) -> list[dict]:
        """
        Extract investment management entities from text.
        
        Args:
            text: Input text
            
        Returns:
            List of extracted entities
        """
        ime_prompt = self.prompt_manager.get_prompt(
            template_name="investment_management_prompt",
            statement_text=text
        )
        ime_result = await self.llm_handler.get_json_from_text_direct(ime_prompt, default_response={})
        
        entities = ime_result.get("investment_management_entities", [])
        
        # Add ID to each entity
        for idx, entity in enumerate(entities, start=1):
            entity["ime_id"] = f"IME{idx:03d}"
        
        # If no entities or no names, try with the elephant prompt
        def has_valid_name(entity):
            return (
                "data" in entity and
                "ime_name" in entity["data"] and
                entity["data"]["ime_name"].get("value")
            )
        
        if not entities or not any(has_valid_name(e) for e in entities):
            ime_elefante_prompt = self.prompt_manager.get_prompt(
                template_name="ime_elefante",
                statement_text=text
            )
            ime_result = await self.llm_handler.get_json_from_text_direct(ime_elefante_prompt, default_response={})
            entities = ime_result.get("investment_management_entities", [])
            for idx, entity in enumerate(entities, start=1):
                entity["ime_id"] = f"IME{idx:03d}"
        
        # If multiple entities, validate to get the main one
        if len(entities) > 1:
            validation_prompt = (
                "Given the following extracted Investment Management Entities:\n"
                f"{json.dumps(entities, indent=2)}\n"
                "Based on the context, which one is the main entity for the financial statement? "
                "Return only the main entity as a JSON array."
            )
            second_entities = await self.llm_handler.get_json_from_text_direct(validation_prompt, default_response={})
            
            try:
                final_entities = second_entities.get("result", second_entities)
                # Ensure result is a list
                if isinstance(final_entities, dict):
                    final_entities = [final_entities]
                return final_entities
            except (json.JSONDecodeError, TypeError) as e:
                LOGGER.error(f"Error decoding validation result: {e}")
                return []
        else:
            return entities if isinstance(entities, list) else [entities]

    async def get_dates(self, text: str, entity: dict) -> list[dict]:
        """
        Extract dates for an entity.
        
        Args:
            text: Input text
            entity: Entity to extract dates for
            
        Returns:
            List of extracted dates
        """
        dates_prompt = self.prompt_manager.get_prompt(
            template_name="dates_prompt",
            statement_text=text,
            ime_id=entity.get("ime_id", ""),
            ime_name=entity["data"]["ime_name"]["value"]
        )
        dates_result = await self.llm_handler.get_json_from_text_direct(dates_prompt, default_response={})
        
        if dates_result:
            try:
                dates_list = dates_result.get("date", [])   
                for idx, date in enumerate(dates_list, start=1):
                    date["id"] = f"DATE{idx:03d}" 
                return dates_list
            except (json.JSONDecodeError, TypeError) as e:
                LOGGER.error(f"Error decoding JSON from LLM response: {e}")
        
        return []
    
    async def get_ime_account_type(self, text: str, entity: dict) -> list[dict]:
        """
        Extract IME account types for an entity.
        
        Args:
            text: Input text
            entity: Entity to extract accounts for
            
        Returns:
            List of extracted accounts
        """
        ime_account_type_prompt = self.prompt_manager.get_prompt(
            template_name="investment_management_account_type_prompt",
            statement_text=text,
            ime_id=entity.get("ime_id", ""),
            ime_name=entity["data"]["ime_name"]["value"]
        )
        ime_accounts = await self.llm_handler.get_json_from_text_direct(ime_account_type_prompt, default_response={})
        accounts = ime_accounts.get("accounts", [])
        
        for idx, acc in enumerate(accounts, start=1):
            if "data" in acc and "number" in acc["data"]:
                acc["data"]["ime_acc_number"] = acc["data"].pop("number")
            acc["ime_acc_id"] = f"IMEACC{idx:03d}"
            acc.pop("descriptor", None)
        
        if len(accounts) == 1 and self.all_fields_null(accounts[0]):
            return []
        
        return accounts
    
    async def get_pse_plan(self, text: str, ps: dict) -> list[dict]:
        """
        Extract PSE plans for a plan sponsor.
        
        Args:
            text: Input text
            ps: Plan sponsor entity
            
        Returns:
            List of extracted plans
        """
        entity_name = ""
        entity_name += f'\n- Investment Management Entity: {ps["data"]["ime_name"]["value"]}' if "ime_name" in ps else ""
        entity_name += f'\n- Plan Sponsor ID: {ps.get("ps_id", "")}'
        entity_name += f'\n- Plan Sponsor Name: {ps["data"]["ps_name"]["value"]}' if "ps_name" in ps else ""
        
        ime_account_type_prompt = self.prompt_manager.get_prompt(
            template_name="plan_sponsor_plan_prompt",
            statement_text=text,
            entity_details=entity_name
        )
        ps_plans = await self.llm_handler.get_json_from_text_direct(ime_account_type_prompt, default_response={})
        plans = ps_plans.get("ps_accounts", [])
        
        # Add ID to each plan
        for idx, plan in enumerate(plans, start=1):
            plan["id"] = f"PSACC{idx:03d}"
            plan.pop("descriptor", None)
        
        if len(plans) == 1 and self.all_fields_null(plans[0]):
            return []
        
        return plans
    
    async def get_ps(self, text: str, entity: dict):
        """
        Extract plan sponsors for an entity.
        
        Args:
            text: Input text
            entity: Entity to extract plan sponsors for
            
        Returns:
            List of extracted plan sponsors
        """
        ime_account_type_prompt = self.prompt_manager.get_prompt(
            template_name="plan_sponsor_prompt",
            statement_text=text,
            entity_details=f"{entity.get('ime_id', '')} {entity.get('descriptor', '')}"
        )
        plan_sponsor = await self.llm_handler.get_json_from_text_direct(ime_account_type_prompt, default_response={})
        ps_list = plan_sponsor.get("plan_sponsor_entities", [])
        
        if len(ps_list) == 1 and self.all_fields_null(ps_list[0]):
            return []
        
        for idx, ps in enumerate(ps_list, start=1):
            ps["ps_id"] = f"PS{idx:03d}"
        
        return ps_list
    
    async def join_ime_ps(self, text: str, entities: list[dict], plan_sponsors: list[dict], ime_accounts: list[dict], ps_plans: list[dict]):
        """
        Join IME and PS entities to establish connections.
        
        Args:
            text: Input text
            entities: List of IME entities
            plan_sponsors: List of plan sponsors
            ime_accounts: List of IME accounts
            ps_plans: List of PS plans
            
        Returns:
            Dictionary with connections between entities
        """
        ime_acc_str = ""
        if ime_accounts:
            for ime_acc in ime_accounts:
                ime_acc_str += (
                    f"\n- [ime_id: {ime_acc['ime_id']}] "
                    f"{entities[0]['data']['ime_name']['value']} | Account: [ime_acc_id: {ime_acc['ime_acc_id']}] "
                    f"{ime_acc['data']['ime_acc_name']['value']} | Account number: {ime_acc['data']['ime_acc_number']['value']}"
                )
        else:
            for entity in entities:
                ime_acc_str += f"\n- [ime_id: {entity['ime_id']}] {entity['data']['ime_name']['value']}"
        
        ps_str = ""
        if ps_plans:
            for ps_plan in ps_plans:
                ps_str += (
                    f"\n- [ps_id: {ps_plan['ps_id']}] "
                    f"{next((ps['data']['ps_name']['value'] for ps in plan_sponsors if ps['ps_id'] == ps_plan['ps_id']), '')} | "
                    f"Plan id: [id: {ps_plan['id']}] "
                    f"{ps_plan['data']['ps_plan_name']['value']} | Plan number: {ps_plan['data']['number']['value']}"
                )
        else:
            for ps in plan_sponsors:
                ps_str += f"\n- [ps_id: {ps['ps_id']}] {ps['data']['ps_name']['value']}"
        
        connections_prompt = self.prompt_manager.get_prompt(
            template_name="join_ime_ps",
            statement_text=text,
            ime_acc=ime_acc_str,
            ps_plan=ps_str
        )
        connectors = await self.llm_handler.get_json_from_text_direct(connections_prompt, default_response={})
        return connectors
    
    async def process_holdings(self, improved_text: str, connections: list[dict]):
        """
        Process holdings from improved text.
        
        Args:
            improved_text: Improved text with holdings
            connections: Connections between entities
            
        Returns:
            List of extracted holdings
        """
        connections = connections.get("connections", [])

        if not isinstance(connections, list):
            LOGGER.error("The 'connections' key does not contain a list.")
            return []
        
        holdings = []
        holding_counter = 1
        
        for conn in connections:
            ime = conn.get("ime") or {}
            ime_acc = conn.get("ime_acc") or {}
            ps = conn.get("ps") or {}
            ps_plan = conn.get("ps_plan") or {}

            ime_id = ime.get("ime_id")
            ime_name = ime.get("ime_name")
            ime_acc_id = ime_acc.get("ime_acc_id")
            ime_acc_name = ime_acc.get("ime_acc_name")
            ps_id = ps.get("ps_id")
            ps_name = ps.get("ps_name")
            ps_plan_id = ps_plan.get("ps_plan_id")
            ps_plan_name = ps_plan.get("ps_plan_name")
            
            entity_details = (
                f"Investment Management ID: {ime_id} | Investment Management Name: {ime_name} | "
                f"Investment Management Account ID {ime_acc_id} | Investment Management Account Name {ime_acc_name} | "
                f"Plan Sponsor ID {ps_id} | Plan Sponsor Name: {ps_name} | "
                f"Plan Sponsor Account ID: {ps_plan_id} | Plan Sponsor Account Name: {ps_plan_name}"
            )
            
            try:
                holdings_prompt = self.prompt_manager.get_prompt(
                    template_name="holdings_prompt",
                    entity_details=entity_details,
                    improved_texts_full=improved_text
                )
                holdings_json = await self.llm_handler.get_json_from_text_direct(holdings_prompt, default_response={})
                
                LOGGER.info(
                    "Holdings extracted as json for connection: ime_id=%s, ime_acc_id=%s, ps_id=%s, ps_plan_id=%s",
                    ime_id, ime_acc_id, ps_id, ps_plan_id
                )
                
                for holding in holdings_json.get("holdings", []):
                    holding["ime_id"] = ime_id
                    holding["ime_acc_id"] = ime_acc_id
                    holding["ps_id"] = ps_id
                    holding["ps_plan_id"] = ps_plan_id
                    holding["h_id"] = f"H{holding_counter:03d}"
                    holding_counter += 1
                
                holdings.extend(holdings_json.get("holdings", []))
            except Exception as e:
                LOGGER.error(
                    "Error extracting holdings %s | continue with next entity | %s", ime_id, e
                )
                continue
        
        return holdings
    
    async def process_transactions(self, improved_text: str, connections: list[dict]):
        """
        Process transactions from improved text.
        
        Args:
            improved_text: Improved text with transactions
            connections: Connections between entities
            
        Returns:
            List of extracted transactions
        """
        connections = connections.get("connections", [])

        if not isinstance(connections, list):
            LOGGER.error("The 'connections' key does not contain a list.")
            return []
        
        transactions = []
        counter = 1
        
        for conn in connections:
            ime = conn.get("ime") or {}
            ime_acc = conn.get("ime_acc") or {}
            ps = conn.get("ps") or {}
            ps_plan = conn.get("ps_plan") or {}
            
            ime_id = ime.get("ime_id")
            ime_name = ime.get("ime_name")
            ime_acc_id = ime_acc.get("ime_acc_id")
            ime_acc_name = ime_acc.get("ime_acc_name")
            ps_id = ps.get("ps_id")
            ps_name = ps.get("ps_name")
            ps_plan_id = ps_plan.get("ps_plan_id")
            ps_plan_name = ps_plan.get("ps_plan_name")
            
            entity_details = (
                f"Investment Management ID: {ime_id} | Investment Management Name: {ime_name} | "
                f"Investment Management Account ID {ime_acc_id} | Investment Management Account Name {ime_acc_name} | "
                f"Plan Sponsor ID {ps_id} | Plan Sponsor Name: {ps_name} | "
                f"Plan Sponsor Account ID: {ps_plan_id} | Plan Sponsor Account Name: {ps_plan_name}"
            )
            
            try:
                transactions_prompt = self.prompt_manager.get_prompt(
                    template_name="transactions_prompt",
                    entity_details=entity_details,
                    improved_texts_full=improved_text
                )
                transactions_json = await self.llm_handler.get_json_from_text_direct(transactions_prompt, default_response={})
                
                LOGGER.info(
                    "Transactions extracted as json for connection: ime_id=%s, ime_acc_id=%s, ps_id=%s, ps_plan_id=%s",
                    ime_id, ime_acc_id, ps_id, ps_plan_id
                )
                
                for transaction in transactions_json.get("transactions", []):
                    transaction["ime_id"] = ime_id
                    transaction["ime_acc_id"] = ime_acc_id
                    transaction["ps_id"] = ps_id
                    transaction["ps_plan_id"] = ps_plan_id
                    transaction["t_id"] = f"T{counter:03d}"
                    counter += 1
                
                transactions.extend(transactions_json.get("transactions", []))
            except Exception as e:
                LOGGER.error(
                    "Error extracting transactions %s | continue with next entity | %s", ime_id, e
                )
                continue
        
        return transactions
    
    def chunk_text_sliding_window(self, text: str, chunk_length: int = 10000, chunk_overlap: int = 1000) -> list[str]:
        """
        Chunk text into overlapping segments using a sliding window approach.
        
        Args:
            text: Input text to chunk
            chunk_length: Maximum length of each chunk
            chunk_overlap: Number of characters to overlap between chunks
            
        Returns:
            List of text chunks
        """
        if not text:
            return []
        
        statement_length = len(text)
        if chunk_overlap >= chunk_length:
            raise ValueError("chunk_overlap must be less than chunk_length.")
        
        # If text is shorter than chunk_length, return it as one chunk
        if statement_length <= chunk_length:
            return [text]
        
        chunks = []
        start_index = 0
        # Calculate the step size for the window's start
        step = chunk_length - chunk_overlap
        
        while start_index < statement_length:
            # Determine the end index for the current chunk
            end_index = min(start_index + chunk_length, statement_length)
            
            # Find nearest paragraph boundaries
            start_index_break_line = text.rfind("\n\n", 0, start_index)
            end_index_break_line = text.find("\n\n", end_index)
            
            start = min(start_index, (start_index_break_line if start_index_break_line >= 0 else 0))
            end = max(end_index, end_index_break_line if end_index_break_line >= 0 else end_index)
            
            # Extract the chunk
            chunk = text[start:end]
            chunks.append(chunk)
            
            # If the end_index reached the end of the text, we're done
            if end_index == statement_length:
                break
            
            # Move the start index forward for the next chunk
            start_index += step
        
        return chunks
    
    async def improve_text(self, connections: list[dict], chunks: list[str], prompt: str) -> str:
        """
        Improve text chunks using LLM.
        
        Args:
            connections: Connections between entities
            chunks: Text chunks to improve
            prompt: Prompt template to use
            
        Returns:
            Improved text
        """
        connections = connections.get("connections", [])

        if not isinstance(connections, list):
            LOGGER.error("The 'connections' key does not contain a list.")
            return []
        
        entities_list = []
        for conn in connections:
            # Safely get ime_name
            ime_value = conn.get("ime", {})
            ime_name = ime_value.get("ime_name", "N/A") if isinstance(ime_value, dict) else "N/A"

            # Safely get ime_acc_name
            ime_acc_value = conn.get("ime_acc", {})
            ime_acc_name = ime_acc_value.get("ime_acc_name", "N/A") if isinstance(ime_acc_value, dict) else "N/A"

            # Safely get ps_name
            ps_value = conn.get("ps", {})
            ps_name = ps_value.get("ps_name", "N/A") if isinstance(ps_value, dict) else "N/A"

            # Safely get ps_plan_name
            ps_plan_value = conn.get("ps_plan", {})
            ps_plan_name = ps_plan_value.get("ps_plan_name", "N/A") if isinstance(ps_plan_value, dict) else "N/A"

            entities_list.append(
                f"- IME name: {ime_name or 'N/A'} | IME Account name: {ime_acc_name or 'N/A'} | Plan Sponsor name: {ps_name or 'N/A'} | Plan Sponsor Plan name: {ps_plan_name or 'N/A'}"
            )
        entities_str = "\n".join(entities_list)
        improved_texts = []
        
        for i, chunk in enumerate(chunks):
            improving_chunk_prompt = self.prompt_manager.get_prompt(
                template_name=prompt,
                chunk=chunk,
                entities_and_accounts=entities_str
            )
            response = await self.llm_handler.get_text_response(improving_chunk_prompt)
            response_2 = f"<START CHUNK {i+1}>\n{response}\n<END CHUNK {i+1}>"
            improved_texts.append(response_2)
        
        improved_texts_full = "\n\n".join(improved_texts)
        return improved_texts_full
    
    async def remove_duplicates(self, data: list[dict]) -> list[dict]:
        """
        Remove duplicate entries from a list.
        
        Args:
            data: List of dictionaries to deduplicate
            
        Returns:
            Deduplicated list
        """
        unique_data = []
        seen_combinations = set()
        
        for text in data:
            name = text['name']
            entity_id = text['investment_management_entity_id']
            combination = (name, entity_id)
            
            if combination not in seen_combinations:
                unique_data.append(text)
                seen_combinations.add(combination)
        
        return unique_data
    
    def format_in_markdown(self, df: pd.DataFrame):
        """
        Format dataframe content as markdown.
        
        Args:
            df: Dataframe with document content
            
        Returns:
            Formatted markdown string
        """
        markdown_lines = []
        
        if df.empty:
            return ""
        
        # Group by page_number
        for page, group in df.groupby("page"):
            markdown_lines.append(f"\n\n### Page {page}\n")
            
            for _, row in group.iterrows():
                content = row['content']
                x0 = row['x0']
                y0 = row['y0']
                x1 = row['x1']
                y1 = row['y1']
                
                if row["type"] == "paragraph":
                    markdown_lines.append(f"\n{content}")
                elif row["type"] == "table":
                    markdown_lines.append("\n#### TABLE START")
                    markdown_lines.append(content)
                    markdown_lines.append("#### TABLE END\n")
        
        # Join all lines into a single string
        markdown_output = "\n".join(markdown_lines)
        return markdown_output
    
    def replace_placeholder(self, final_result: dict) -> dict:
        """
        Replace placeholder values in the result.
        
        Args:
            final_result: Result dictionary to process
            
        Returns:
            Processed result dictionary
        """
        # Replace in investment_management_entities
        for entity in final_result.get("investment_management_entity", []):
            if entity.get("data", {}).get("ime_name", {}).get("value") == "ELEFANTE ROSA":
                entity["data"]["ime_name"]["value"] = None
        
        # Replace in date
        for date in final_result.get("date", []):
            if date.get("ime_name") == "ELEFANTE ROSA":
                date["ime_name"] = None
        
        # Replace in ime_accounts
        for acc in final_result.get("ime_account", []):
            if acc.get("data", {}).get("ime_acc_name", {}).get("value") == "ELEFANTE ROSA":
                acc["data"]["ime_acc_name"]["value"] = None
        
        # Replace in plan_sponsors
        for ps in final_result.get("plan_sponsor", []):
            if ps.get("data", {}).get("ps_name", {}).get("value") == "ELEFANTE ROSA":
                ps["data"]["ps_name"]["value"] = None
        
        # Replace in ps_accounts
        for psa in final_result.get("ps_account", []):
            if psa.get("data", {}).get("ps_plan_name", {}).get("value") == "ELEFANTE ROSA":
                psa["data"]["ps_plan_name"]["value"] = None
        
        # Replace in holdings
        for h in final_result.get("holding", []):
            if h.get("data", {}).get("name", {}).get("value") == "ELEFANTE ROSA":
                h["data"]["name"]["value"] = None
        
        # Replace in transactions
        for t in final_result.get("transaction", []):
            if t.get("data", {}).get("name", {}).get("value") == "ELEFANTE ROSA":
                t["data"]["name"]["value"] = None
        
        return final_result
    
    def validate_and_apply_mappings(self, ime_accounts, ps_plans, mappings):
        """
        Validate and apply mappings between IME accounts and PS plans.
        
        Args:
            ime_accounts: List of IME accounts
            ps_plans: List of PS plans
            mappings: Mappings to apply
            
        Returns:
            Updated PS plans with applied mappings
        """
        # Get valid IME account IDs
        valid_ime_acc_ids = {acc["ime_acc_id"] for acc in ime_accounts}
        LOGGER.info(f"Valid IME account IDs: {valid_ime_acc_ids}")
        
        # Create validation dictionary
        validated_mapping_dict = {}
        for m in mappings:
            if m.get("imeacc_id") in valid_ime_acc_ids:
                key = (m.get("id"), m.get("ps_id")) 
                validated_mapping_dict[key] = m.get("imeacc_id")
        
        # Apply mappings
        for plan in ps_plans:
            key = (plan.get("id"), plan.get("ps_id"))
            plan["imeacc_id"] = validated_mapping_dict.get(key, "")
            
            if plan["imeacc_id"]:
                LOGGER.info(f"Successfully mapped plan {key} to {plan['imeacc_id']}")
            else:
                LOGGER.warning(f"No mapping found for plan {key}")
        
        return ps_plans
    
    async def get_ps_imeacc_mapping(self, plan_sponsors: list, ime_accounts: list, ps_accounts: list):
        """
        Get mappings between PS accounts and IME accounts.
        
        Args:
            plan_sponsors: List of plan sponsors
            ime_accounts: List of IME accounts
            ps_accounts: List of PS accounts
            
        Returns:
            Updated PS accounts with mappings
        """
        mapping_prompt = self.prompt_manager.get_prompt(
            template_name="imeacc_id_prompt",
            ime_accounts=json.dumps(ime_accounts, indent=2),
            plan_sponsors=json.dumps(plan_sponsors, indent=2),
            ps_accounts=json.dumps(ps_accounts, indent=2)
        )
        mapping_response = await self.llm_handler.get_json_from_text_direct(mapping_prompt, default_response={})
        mappings = mapping_response.get("account_mappings", [])
        
        # Validate and apply mappings
        validated_ps_accounts = self.validate_and_apply_mappings(ime_accounts, ps_accounts, mappings)
        
        return validated_ps_accounts
    
    def create_final_json(self, data: dict) -> dict:
        """
        Create the final JSON structure.
        
        Args:
            data: Input data to structure
            
        Returns:
            Structured JSON data
        """
        final_json = {
            "document_id": data.get("document_id", ""),
            "investment_management_entity": [
                {
                    "id": self.__format_ids(entity.get("ime_id", "")),
                    "data": {
                        "name": {
                            "value": entity.get("data", {}).get("ime_name", {}).get("value", ""),
                            "page_number": entity.get("data", {}).get("ime_name", {}).get("page_number", None),
                        },
                        "address": {
                            "value": entity.get("data", {}).get("address", {}).get("value", ""),
                            "page_number": entity.get("data", {}).get("address", {}).get("page_number", None),
                        },
                        "website": {
                            "value": entity.get("data", {}).get("website", {}).get("value", ""),
                            "page_number": entity.get("data", {}).get("website", {}).get("page_number", None),
                        },
                        "entity_unique_id": {
                            "value": entity.get("data", {}).get("entity_unique_id", {}).get("value", ""),
                            "page_number": entity.get("data", {}).get("entity_unique_id", {}).get("page_number", None),
                        }
                    }
                } for entity in data.get("investment_management_entity", [])
            ],
            "date": [
                {
                    "id": self.__format_ids(date.get("id", "")),
                    "ime_id": self.__format_ids(date.get("ime_id", "")),
                    "data": {
                        "start_date": {
                            "value": date.get("data", {}).get("start_date", {}).get("value", ""),
                            "page_number": date.get("data", {}).get("start_date", {}).get("page_number", None),
                        },
                        "end_date": {
                            "value": date.get("data", {}).get("end_date", {}).get("value", ""),
                            "page_number": date.get("data", {}).get("end_date", {}).get("page_number", None),
                        }
                    }
                } for date in data.get("date", [])
            ],
            "plan_sponsor": [
                {
                    "id": self.__format_ids(ps.get("ps_id", "")),
                    "ime_id": self.__format_ids(ps.get("ime_id", "")),
                    "data": {
                        "name": {
                            "value": ps.get("data", {}).get("ps_name", {}).get("value", ""),
                            "page_number": ps.get("data", {}).get("ps_name", {}).get("page_number", None),
                        },
                        "address": {
                            "value": ps.get("data", {}).get("address", {}).get("value", ""),
                            "page_number": ps.get("data", {}).get("address", {}).get("page_number", None),
                        },
                        "website": {
                            "value": ps.get("data", {}).get("website", {}).get("value", ""),
                            "page_number": ps.get("data", {}).get("website", {}).get("page_number", None),
                        },
                        "entity_unique_id": {
                            "value": ps.get("data", {}).get("entity_unique_id", {}).get("value", ""),
                            "page_number": ps.get("data", {}).get("entity_unique_id", {}).get("page_number", None),
                        }
                    }
                } for ps in data.get("plan_sponsor", [])
            ],
            "ime_account": [
                {
                    "id": self.__format_ids(ime_acc.get("ime_acc_id", "")),
                    "ime_id": self.__format_ids(ime_acc.get("ime_id", "")),
                    "margin_account": ime_acc.get("margin_account", ""),
                    "data": {
                        "name": {
                            "value": ime_acc.get("data", {}).get("ime_acc_name", {}).get("value", ""),
                            "page_number": ime_acc.get("data", {}).get("ime_acc_name", {}).get("page_number", None),
                        },
                        "number": {
                            "value": ime_acc.get("data", {}).get("ime_acc_number", {}).get("value", ""),
                            "page_number": ime_acc.get("data", {}).get("ime_acc_number", {}).get("page_number", None),
                        }
                    }
                } for ime_acc in data.get("ime_account", [])
            ],
            "ps_account": [
                {
                    "id": self.__format_ids(ps_acc.get("id", "")),
                    "imeacc_id": self.__format_ids(ps_acc.get("imeacc_id", "")),
                    "ps_id": self.__format_ids(ps_acc.get("ps_id", "")),
                    "data": {
                        "name": {
                            "value": ps_acc.get("data", {}).get("ps_plan_name", {}).get("value", ""),
                            "page_number": ps_acc.get("data", {}).get("ps_plan_name", {}).get("page_number", None),
                        },
                        "number": {
                            "value": ps_acc.get("data", {}).get("number", {}).get("value", ""),
                            "page_number": ps_acc.get("data", {}).get("number", {}).get("page_number", None),
                        }
                    }
                } for ps_acc in data.get("ps_account", [])
            ],
            "holding": [
                {
                    "id": self.__format_ids(holding.get("h_id", "")),
                    "ime_id": self.__format_ids(holding.get("ime_id", "")),
                    "imeacc_id": self.__format_ids(holding.get("ime_acc_id", "")),
                    "ps_id": self.__format_ids(holding.get("ps_id", "")),
                    "psacc_id": self.__format_ids(holding.get("ps_plan_id", "")),
                    "ht_id": self.__format_ids(holding.get("ht_id", "")),
                    "data": {
                        "name": {
                            "value": holding.get("data", {}).get("name", {}).get("value", ""),
                            "page_number": holding.get("data", {}).get("name", {}).get("page_number", None),
                        },
                        "ticker": {
                            "value": holding.get("data", {}).get("ticker", {}).get("value", ""),
                            "page_number": holding.get("data", {}).get("ticker", {}).get("page_number", None),
                        },
                        "class": {
                            "value": holding.get("data", {}).get("class", {}).get("value", ""),
                            "page_number": holding.get("data", {}).get("class", {}).get("page_number", None),
                        },
                        "type": {
                            "value": holding.get("data", {}).get("type", {}).get("value", ""),
                            "page_number": holding.get("data", {}).get("type", {}).get("page_number", None),
                        },
                        "exchange": {
                            "value": holding.get("data", {}).get("exchange", {}).get("value", ""),
                            "page_number": holding.get("data", {}).get("exchange", {}).get("page_number", None),
                        },
                        "currency": {
                            "value": holding.get("data", {}).get("currency", {}).get("value", ""),
                            "page_number": holding.get("data", {}).get("currency", {}).get("page_number", None)
                        },
                        "beginning_value": {
                            "value": holding.get("data", {}).get("beginning_value", {}).get("value", ""),
                            "page_number": holding.get("data", {}).get("beginning_value", {}).get("page_number", None),
                        },
                        "ending_value": {
                            "value": holding.get("data", {}).get("ending_value", {}).get("value", ""),
                            "page_number": holding.get("data", {}).get("ending_value", {}).get("page_number", None),
                        },
                        "security_unique_id": {
                            "value": holding.get("data", {}).get("security_unique_id", {}).get("value", ""),
                            "page_number": holding.get("data", {}).get("security_unique_id", {}).get("page_number", None),
                        }
                    }
                } for holding in data.get("holding", [])
            ],
            "transaction": [
                {
                    "id": self.__format_ids(txn.get("t_id", "")),
                    "ime_id": self.__format_ids(txn.get("ime_id", "")),
                    "imeacc_id": self.__format_ids(txn.get("ime_acc_id", "")),
                    "ps_id": self.__format_ids(txn.get("ps_id", "")),
                    "psacc_id": self.__format_ids(txn.get("ps_plan_id", "")),
                    "tt_id": self.__format_ids(txn.get("tt_id", "")),
                    "data": {
                        "name": {
                            "value": txn.get("data", {}).get("name", {}).get("value", ""),
                            "page_number": txn.get("data", {}).get("name", {}).get("page_number", None),
                        },
                        "transaction_date": {
                            "value": txn.get("data", {}).get("transaction_date", {}).get("value", ""),
                            "page_number": txn.get("data", {}).get("transaction_date", {}).get("page_number", None),
                        },
                        "transaction_type": {
                            "value": txn.get("data", {}).get("transaction_type", {}).get("value", ""),
                            "page_number": txn.get("data", {}).get("transaction_type", {}).get("page_number", None),
                        },
                        "ticker": {
                            "value": txn.get("data", {}).get("ticker", {}).get("value", ""),
                            "page_number": txn.get("data", {}).get("ticker", {}).get("page_number", None),
                        },
                        "class": {
                            "value": txn.get("data", {}).get("class", {}).get("value", ""),
                            "page_number": txn.get("data", {}).get("class", {}).get("page_number", None),
                        },
                        "type": {
                            "value": txn.get("data", {}).get("type", {}).get("value", ""),
                            "page_number": txn.get("data", {}).get("type", {}).get("page_number", None),
                        },
                        "exchange": {
                            "value": txn.get("data", {}).get("exchange", {}).get("value", ""),
                            "page_number": txn.get("data", {}).get("exchange", {}).get("page_number", None),
                        },
                        "currency": {
                            "value": txn.get("data", {}).get("currency", {}).get("value", ""),
                            "page_number": txn.get("data", {}).get("currency", {}).get("page_number", None)
                        },
                        "investment_value": {
                            "value": txn.get("data", {}).get("investment_value", {}).get("value", ""),
                            "page_number": txn.get("data", {}).get("investment_value", {}).get("page_number", None),
                        },
                        "security_unique_id": {
                            "value": txn.get("data", {}).get("security_unique_id", {}).get("value", ""),
                            "page_number": txn.get("data", {}).get("security_unique_id", {}).get("page_number", None),
                        }
                    }
                } for txn in data.get("transaction", [])
            ],
        }
        return final_json
    
    def __format_ids(self, id_str: str):
        """
        Format IDs to match the required pattern.
        
        Args:
            id_str: ID string to format
            
        Returns:
            Formatted ID string
        """
        if not id_str or not isinstance(id_str, str) or len(id_str) < 2:
            return id_str
        
        # Extract prefix and numeric parts
        p1 = re.findall(r'^[a-zA-Z]+', id_str)
        p1 = p1[0] if p1 else ""
        p2 = re.findall(r'\d+$', id_str)
        p2 = p2[0] if p2 else ""
        
        return f"{p1}_{p2}" if p1 and p2 else id_str