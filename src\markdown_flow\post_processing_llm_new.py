import json
import pandas as pd
import re
import asyncio
from src.services.llm_handler import LLMHandler
from src.services.prompt_manager import PromptManager

from src.utils.logger import get_logger
LOGGER = get_logger("src.markdown_flow.post_processing_llm_new")

class PostProcessingLLM:
    """
    ASYNC VERSION: Post-processing class using the "Anchor and Associate" model.
    This approach is more robust by first finding key data tables (anchors)
    and then associating them with the correct entities. This minimizes error
    propagation from a fragile, sequential extraction process. All I/O-bound
    (LLM) calls are run concurrently for maximum performance.
    """
    
    def __init__(self, llm_handler: LLMHandler):
        """
        Initialize the post-processing LLM handler.
        
        Args:
            llm_handler: Handler for LLM interactions. Must support async calls.
        """
        self.llm_handler = llm_handler
        self.prompt_manager = PromptManager(llm_handler.prompts_dir)

    async def run(self, df: pd.DataFrame) -> dict:
        """
        Run the "Anchor and Associate" post-processing pipeline asynchronously.
        """
        LOGGER.info("Starting 'Anchor and Associate' extraction pipeline.")
        
        final_result = {
            "document_id": None, 
            "investment_management_entity": [],
            "date": [],
            "plan_sponsor": [],
            "ime_account": [],
            "ps_account": [],
            "holding": [],
            "transaction": []
        }
        
        markdown_output = self.format_in_markdown(df)
        if not markdown_output.strip():
            LOGGER.warning("Input document text is empty. Aborting pipeline.")
            return final_result

        LOGGER.info("--- PHASE 1: ANCHOR & COLLECT (Running in parallel) ---")
        
        # ASYNC: Run entity collection and table finding concurrently.
        phase1_tasks = [
            self._phase1_collect_all_potential_entities(markdown_output),
            self._phase1_anchor_find_candidate_tables(markdown_output)
        ]
        phase1_results = await asyncio.gather(*phase1_tasks)
        
        potential_entities = phase1_results[0]
        candidate_tables = phase1_results[1]

        final_result.update(potential_entities)
        LOGGER.info(f"Collected {len(potential_entities.get('investment_management_entity', []))} IMEs, "
                     f"{len(potential_entities.get('plan_sponsor', []))} PSEs, "
                     f"{len(potential_entities.get('ime_account', []))} IME Accounts.")

        if not candidate_tables:
            LOGGER.warning("No candidate holdings or transaction tables found. Extraction will be limited to entities.")
            # Aseguramos que el resultado final pase por los reemplazos y formato antes de devolverlo
            final_result = self.replace_placeholder(final_result)
            return self.create_final_json(final_result)
            
        LOGGER.info(f"Found {len(candidate_tables)} candidate tables to analyze.")

        LOGGER.info("--- PHASE 2: ASSOCIATE TABLES TO ENTITIES (Running in parallel) ---")
        associated_tables = await self._phase2_associate_tables_to_entities(
            markdown_output, candidate_tables, potential_entities
        )
        LOGGER.info(f"Successfully processed associations for {len(associated_tables)} tables.")

        LOGGER.info("--- PHASE 3: DETAILED & FINAL EXTRACTION (Running in parallel) ---")
        detailed_data = await self._phase3_extract_detailed_data(associated_tables)
        final_result["holding"] = detailed_data.get("holdings", [])
        final_result["transaction"] = detailed_data.get("transactions", [])
        LOGGER.info(f"Extracted {len(final_result['holding'])} holdings and {len(final_result['transaction'])} transactions.")

        final_result = self.replace_placeholder(final_result)
        final_result = self.create_final_json(final_result)
        LOGGER.info("Pipeline finished successfully.")
        
        return final_result

    async def _phase1_collect_all_potential_entities(self, text: str) -> dict:
        """
        Runs all entity extraction prompts concurrently to gather a complete list of potential entities.
        """
        entities_collection = {}

        imes = await self.get_ime(text)
        entities_collection["investment_management_entity"] = imes
        
        if not imes:
            LOGGER.warning("No Investment Management Entities found. Further entity extraction will be skipped.")
            entities_collection.update({"date": [], "ime_account": [], "plan_sponsor": [], "ps_account": []})
            return entities_collection

        # ASYNC: Create tasks for all sub-entity extractions to run them in parallel.
        entity_sub_tasks = []
        for entity in imes:
            entity_sub_tasks.append(self.get_dates(text, entity))
            entity_sub_tasks.append(self.get_ime_account_type(text, entity))
            entity_sub_tasks.append(self.get_ps(text, entity))
        
        results = await asyncio.gather(*entity_sub_tasks)
        
        # ASYNC: Deconstruct the results from asyncio.gather
        all_dates = [item for sublist in results[0::3] if sublist for item in sublist]
        all_ime_accounts = [item for sublist in results[1::3] if sublist for item in sublist]
        all_plan_sponsors = [item for sublist in results[2::3] if sublist for item in sublist]

        entities_collection["date"] = all_dates
        entities_collection["ime_account"] = all_ime_accounts
        entities_collection["plan_sponsor"] = all_plan_sponsors

        # ASYNC: Parallelize PS Plan extraction if plan sponsors were found
        if all_plan_sponsors:
            ps_plan_tasks = [self.get_pse_plan(text, ps) for ps in all_plan_sponsors]
            ps_plan_results = await asyncio.gather(*ps_plan_tasks)
            all_ps_plans = [plan for sublist in ps_plan_results if sublist for plan in sublist]

            if all_ps_plans and all_ime_accounts:
                all_ps_plans = await self.get_ps_imeacc_mapping(all_plan_sponsors, all_ime_accounts, all_ps_plans)
            
            entities_collection["ps_account"] = all_ps_plans
        else:
            entities_collection["ps_account"] = []
            
        return entities_collection

    async def _phase1_anchor_find_candidate_tables(self, text: str) -> list[dict]:
        """
        Uses the 'Table Detector Prompt' to find all potential holdings and transaction tables.
        """
        table_detector_prompt = self.prompt_manager.get_prompt(
            template_name="table_detector_prompt",
            full_document_text=text
        )
        response = await self.llm_handler.get_json_from_text_direct(table_detector_prompt, default_response={})
        return response.get("candidate_tables", [])

    async def _associate_single_table(self, full_text: str, table: dict, entities_json_str: str) -> dict | None:
        """Coroutine to process a single table's association."""
        surrounding_text = self._get_surrounding_text(full_text, table["raw_content"])
        
        association_prompt = self.prompt_manager.get_prompt(
            template_name="table_association_prompt",
            table_id=table["table_id"],
            page_number=table["page_number"],
            table_type=table["table_type"],
            raw_content=table["raw_content"],
            surrounding_text=surrounding_text,
            potential_entities_json=entities_json_str
        )
        
        association_result = await self.llm_handler.get_json_from_text_direct(association_prompt, default_response={})
        
        if association_result and "association" in association_result:
            return {**table, **association_result}
        
        LOGGER.warning(f"Failed to get a valid association for table_id: {table['table_id']}")
        return None

    async def _phase2_associate_tables_to_entities(self, full_text: str, candidate_tables: list, potential_entities: dict) -> list[dict]:
        """
        For each candidate table, determines its owner using the 'Table Association Prompt' in parallel.
        """
        entities_json_str = json.dumps(potential_entities, indent=2)
        association_tasks = [
            self._associate_single_table(full_text, table, entities_json_str)
            for table in candidate_tables
        ]
        results = await asyncio.gather(*association_tasks)
        return [res for res in results if res is not None]

    async def _extract_data_for_single_table(self, table: dict) -> tuple[str, list, dict]:
        """Coroutine to perform detailed extraction for a single table."""
        association = table.get("association", {})
        if not association:
            return (None, [], {})

        entity_details = (
            f"Investment Management ID: {association.get('ime_id')} | Investment Management Name: {association.get('ime_name')} | "
            f"Investment Management Account ID: {association.get('ime_acc_id')} | Investment Management Account Name: {association.get('ime_acc_name')} | "
            f"Plan Sponsor ID: {association.get('ps_id')} | Plan Sponsor Name: {association.get('ps_name')} | "
            f"Plan Sponsor Account ID: {association.get('ps_plan_id')} | Plan Sponsor Account Name: {association.get('ps_plan_name')}"
        )
        table_content = table["raw_content"]
        
        try:
            if table["table_type"] == "HOLDING_CANDIDATE":
                prompt = self.prompt_manager.get_prompt("holdings_prompt", entity_details=entity_details, improved_texts_full=table_content)
                json_result = await self.llm_handler.get_json_from_text_direct(prompt, default_response={})
                return ("holding", json_result.get("holdings", []), association)

            elif table["table_type"] == "TRANSACTION_CANDIDATE":
                prompt = self.prompt_manager.get_prompt("transactions_prompt", entity_details=entity_details, improved_texts_full=table_content)
                json_result = await self.llm_handler.get_json_from_text_direct(prompt, default_response={})
                return ("transaction", json_result.get("transactions", []), association)
        except Exception as e:
            LOGGER.error(f"Error during detailed extraction for table {table.get('table_id')}: {e}")
        
        return (None, [], {})

    async def _phase3_extract_detailed_data(self, associated_tables: list[dict]) -> dict:
        """
        Iterates through associated tables and performs final extraction in parallel.
        """
        extraction_tasks = [self._extract_data_for_single_table(table) for table in associated_tables]
        results = await asyncio.gather(*extraction_tasks)
        
        all_holdings, all_transactions = [], []
        holding_counter, transaction_counter = 1, 1

        for data_type, items, association in results:
            if not items:
                continue
            if data_type == "holding":
                for holding in items:
                    holding.update({
                        "ime_id": association.get("ime_id"), "ime_acc_id": association.get("ime_acc_id"),
                        "ps_id": association.get("ps_id"), "ps_plan_id": association.get("ps_plan_id"),
                        "h_id": f"H{holding_counter:03d}"
                    })
                    holding_counter += 1
                    all_holdings.append(holding)
            elif data_type == "transaction":
                for transaction in items:
                    transaction.update({
                        "ime_id": association.get("ime_id"), "ime_acc_id": association.get("ime_acc_id"),
                        "ps_id": association.get("ps_id"), "ps_plan_id": association.get("ps_plan_id"),
                        "t_id": f"T{transaction_counter:03d}"
                    })
                    transaction_counter += 1
                    all_transactions.append(transaction)
        
        return {"holdings": all_holdings, "transactions": all_transactions}

    async def get_ime(self, text: str) -> list[dict]:
        """Extracts Investment Management Entities with fallback and validation logic."""
        ime_prompt = self.prompt_manager.get_prompt(
            template_name="investment_management_prompt",
            statement_text=text
        )
        ime_result = await self.llm_handler.get_json_from_text_direct(ime_prompt, default_response={})
        entities = ime_result.get("investment_management_entities", [])
        
        for idx, entity in enumerate(entities, start=1):
            entity["ime_id"] = f"IME{idx:03d}"
        
        def has_valid_name(entity):
            return (
                "data" in entity and
                "ime_name" in entity["data"] and
                entity["data"]["ime_name"].get("value")
            )

        if not entities or not any(has_valid_name(e) for e in entities):
            LOGGER.warning("No valid IME found. Trying fallback 'elefante' prompt.")
            ime_elefante_prompt = self.prompt_manager.get_prompt(
                template_name="ime_elefante",
                statement_text=text
            )
            ime_result = await self.llm_handler.get_json_from_text_direct(ime_elefante_prompt, default_response={})
            entities = ime_result.get("investment_management_entities", [])
            for idx, entity in enumerate(entities, start=1):
                entity["ime_id"] = f"IME{idx:03d}"
        
        if len(entities) > 1:
            LOGGER.info(f"Found {len(entities)} entities, running validation to find the main one.")
            validation_prompt = (
                "Given the following extracted Investment Management Entities:\n"
                f"{json.dumps(entities, indent=2)}\n"
                "Based on the context, which one is the main entity for the financial statement? "
                "Return only the main entity as a JSON array."
            )
            second_entities_result = await self.llm_handler.get_json_from_text_direct(validation_prompt, default_response={})
            
            try:
                # The LLM might return the list inside a key like "result" or at the top level
                final_entities = second_entities_result.get("result", second_entities_result)
                if isinstance(final_entities, dict):
                    final_entities = [final_entities]
                return final_entities
            except (json.JSONDecodeError, TypeError) as e:
                LOGGER.error(f"Error decoding validation result: {e}. Returning original multiple entities.")
                return entities

        return entities if isinstance(entities, list) else [entities]

    async def get_dates(self, text: str, entity: dict) -> list[dict]:
        """Extracts statement dates for a given entity."""
        dates_prompt = self.prompt_manager.get_prompt(
            template_name="dates_prompt", 
            statement_text=text, 
            ime_id=entity.get("ime_id", ""), 
            ime_name=entity.get("data", {}).get("ime_name", {}).get("value")
        )
        dates_result = await self.llm_handler.get_json_from_text_direct(dates_prompt, default_response={})
        if dates_result:
            dates_list = dates_result.get("date", [])   
            for idx, date in enumerate(dates_list, start=1):
                date["id"] = f"DATE{idx:03d}"
                date["ime_id"] = entity.get("ime_id") # Associate with the IME
            return dates_list
        return []

    async def get_ime_account_type(self, text: str, entity: dict) -> list[dict]:
        """Extracts IME account types for a given entity."""
        prompt = self.prompt_manager.get_prompt(
            template_name="investment_management_account_type_prompt", 
            statement_text=text, 
            ime_id=entity.get("ime_id", ""), 
            ime_name=entity.get("data", {}).get("ime_name", {}).get("value")
        )
        ime_accounts = await self.llm_handler.get_json_from_text_direct(prompt, default_response={})
        accounts = ime_accounts.get("accounts", [])
        for idx, acc in enumerate(accounts, start=1):
            if "data" in acc and "number" in acc["data"]:
                acc["data"]["ime_acc_number"] = acc["data"].pop("number")
            acc["ime_acc_id"] = f"IMEACC{idx:03d}"
            acc["ime_id"] = entity.get("ime_id") # Associate with the IME
            acc.pop("descriptor", None)
        return accounts

    async def get_ps(self, text: str, entity: dict) -> list[dict]:
        """Extracts Plan Sponsors for a given entity."""
        prompt = self.prompt_manager.get_prompt(
            template_name="plan_sponsor_prompt", 
            statement_text=text, 
            entity_details=f"{entity.get('ime_id', '')} {entity.get('data', {}).get('ime_name', {}).get('value', '')}"
        )
        plan_sponsor = await self.llm_handler.get_json_from_text_direct(prompt, default_response={})
        ps_list = plan_sponsor.get("plan_sponsor_entities", [])
        for idx, ps in enumerate(ps_list, start=1):
            ps["ps_id"] = f"PS{idx:03d}"
            ps["ime_id"] = entity.get("ime_id") # Associate with the IME
        return ps_list

    async def get_pse_plan(self, text: str, ps: dict) -> list[dict]:
        """Extracts PSE plans for a given plan sponsor."""
        entity_details = f"Plan Sponsor Name: {ps.get('data', {}).get('ps_name', {}).get('value', '')}"
        prompt = self.prompt_manager.get_prompt(
            template_name="plan_sponsor_plan_prompt", 
            statement_text=text, 
            entity_details=entity_details
        )
        ps_plans = await self.llm_handler.get_json_from_text_direct(prompt, default_response={})
        plans = ps_plans.get("ps_accounts", [])
        for idx, plan in enumerate(plans, start=1):
            plan["id"] = f"PSACC{idx:03d}"
            plan["ps_id"] = ps.get("ps_id") # Associate with the PS
            plan.pop("descriptor", None)
        return plans

    async def get_ps_imeacc_mapping(self, plan_sponsors: list, ime_accounts: list, ps_accounts: list) -> list:
        """Gets mappings between PS accounts and IME accounts."""
        prompt = self.prompt_manager.get_prompt(
            template_name="imeacc_id_prompt", 
            ime_accounts=json.dumps(ime_accounts, indent=2), 
            plan_sponsors=json.dumps(plan_sponsors, indent=2), 
            ps_accounts=json.dumps(ps_accounts, indent=2)
        )
        mapping_response = await self.llm_handler.get_json_from_text_direct(prompt, default_response={})
        mappings = mapping_response.get("account_mappings", [])
        return self.validate_and_apply_mappings(ime_accounts, ps_accounts, mappings)

    # --- Métodos Sincrónicos (sin I/O) ---
    
    def _get_surrounding_text(self, full_text: str, table_content: str, window_size: int = 1000) -> str:
        """
        Extracts text surrounding a specific table's content for better context.
        This is a synchronous helper method as it only performs string manipulation.
        """
        if not table_content or not full_text:
            return ""
        
        # Use a simplified version of the content to find it, avoiding markdown issues
        search_content = re.sub(r'\s+', ' ', table_content).strip()[:200]
        index = full_text.find(search_content)

        if index == -1:
            return "Table content not found in the full document. This might happen with very large tables."

        start = max(0, index - window_size)
        end = min(len(full_text), index + len(table_content) + window_size)
        
        return full_text[start:end]

    def validate_and_apply_mappings(self, ime_accounts: list, ps_plans: list, mappings: list) -> list:
        """Validates and applies mappings from LLM to link PS accounts with IME accounts."""
        valid_ime_acc_ids = {acc["ime_acc_id"] for acc in ime_accounts}
        LOGGER.info(f"Valid IME account IDs for mapping: {valid_ime_acc_ids}")
        
        validated_mapping_dict = {}
        for m in mappings:
            if m.get("imeacc_id") in valid_ime_acc_ids:
                key = (m.get("id"), m.get("ps_id")) 
                validated_mapping_dict[key] = m.get("imeacc_id")
        
        for plan in ps_plans:
            key = (plan.get("id"), plan.get("ps_id"))
            plan["imeacc_id"] = validated_mapping_dict.get(key, "")
            if plan["imeacc_id"]:
                LOGGER.info(f"Successfully mapped plan {key} to {plan['imeacc_id']}")
            else:
                LOGGER.warning(f"No valid mapping found for plan {key}")
                
        return ps_plans

    def format_in_markdown(self, df: pd.DataFrame) -> str:
        """Formats dataframe content as a single markdown string."""
        markdown_lines = []
        if df.empty: return ""
        for page, group in df.groupby("page"):
            markdown_lines.append(f"\n\n### Page {page}\n")
            for _, row in group.iterrows():
                content = row['content']
                if row["type"] == "paragraph":
                    markdown_lines.append(f"\n{content}")
                elif row["type"] == "table":
                    markdown_lines.append("\n#### TABLE START")
                    markdown_lines.append(content)
                    markdown_lines.append("#### TABLE END\n")
        return "\n".join(markdown_lines)

    def replace_placeholder(self, final_result: dict) -> dict:
        """Replaces placeholder values like 'ELEFANTE ROSA' with None."""
        placeholder = "ELEFANTE ROSA"
        for entity in final_result.get("investment_management_entity", []):
            if entity.get("data", {}).get("ime_name", {}).get("value") == placeholder:
                entity["data"]["ime_name"]["value"] = None
        for date in final_result.get("date", []):
            if date.get("ime_name") == placeholder:
                date["ime_name"] = None
        for acc in final_result.get("ime_account", []):
            if acc.get("data", {}).get("ime_acc_name", {}).get("value") == placeholder:
                acc["data"]["ime_acc_name"]["value"] = None
        for ps in final_result.get("plan_sponsor", []):
            if ps.get("data", {}).get("ps_name", {}).get("value") == placeholder:
                ps["data"]["ps_name"]["value"] = None
        for psa in final_result.get("ps_account", []):
            if psa.get("data", {}).get("ps_plan_name", {}).get("value") == placeholder:
                psa["data"]["ps_plan_name"]["value"] = None
        for h in final_result.get("holding", []):
            if h.get("data", {}).get("name", {}).get("value") == placeholder:
                h["data"]["name"]["value"] = None
        for t in final_result.get("transaction", []):
            if t.get("data", {}).get("name", {}).get("value") == placeholder:
                t["data"]["name"]["value"] = None
        return final_result

    def create_final_json(self, data: dict) -> dict:
        """Creates the final, structured JSON output from the extracted data."""
        # Esta función es idéntica a la original y no necesita cambios.
        # Se mantiene por completitud.
        final_json = {
            "document_id": data.get("document_id", ""),
            "investment_management_entity": [
                {
                    "id": self.__format_ids(entity.get("ime_id", "")),
                    "data": {
                        "name": {
                            "value": entity.get("data", {}).get("ime_name", {}).get("value", ""),
                            "page_number": entity.get("data", {}).get("ime_name", {}).get("page_number", None),
                        },
                        "address": {
                            "value": entity.get("data", {}).get("address", {}).get("value", ""),
                            "page_number": entity.get("data", {}).get("address", {}).get("page_number", None),
                        },
                        "website": {
                            "value": entity.get("data", {}).get("website", {}).get("value", ""),
                            "page_number": entity.get("data", {}).get("website", {}).get("page_number", None),
                        },
                        "entity_unique_id": {
                            "value": entity.get("data", {}).get("entity_unique_id", {}).get("value", ""),
                            "page_number": entity.get("data", {}).get("entity_unique_id", {}).get("page_number", None),
                        }
                    }
                } for entity in data.get("investment_management_entity", [])
            ],
            "date": [
                {
                    "id": self.__format_ids(date.get("id", "")),
                    "ime_id": self.__format_ids(date.get("ime_id", "")),
                    "data": {
                        "start_date": {
                            "value": date.get("data", {}).get("start_date", {}).get("value", ""),
                            "page_number": date.get("data", {}).get("start_date", {}).get("page_number", None),
                        },
                        "end_date": {
                            "value": date.get("data", {}).get("end_date", {}).get("value", ""),
                            "page_number": date.get("data", {}).get("end_date", {}).get("page_number", None),
                        }
                    }
                } for date in data.get("date", [])
            ],
            "plan_sponsor": [
                {
                    "id": self.__format_ids(ps.get("ps_id", "")),
                    "ime_id": self.__format_ids(ps.get("ime_id", "")),
                    "data": {
                        "name": {
                            "value": ps.get("data", {}).get("ps_name", {}).get("value", ""),
                            "page_number": ps.get("data", {}).get("ps_name", {}).get("page_number", None),
                        },
                        "address": {
                            "value": ps.get("data", {}).get("address", {}).get("value", ""),
                            "page_number": ps.get("data", {}).get("address", {}).get("page_number", None),
                        },
                        "website": {
                            "value": ps.get("data", {}).get("website", {}).get("value", ""),
                            "page_number": ps.get("data", {}).get("website", {}).get("page_number", None),
                        },
                        "entity_unique_id": {
                            "value": ps.get("data", {}).get("entity_unique_id", {}).get("value", ""),
                            "page_number": ps.get("data", {}).get("entity_unique_id", {}).get("page_number", None),
                        }
                    }
                } for ps in data.get("plan_sponsor", [])
            ],
            "ime_account": [
                {
                    "id": self.__format_ids(ime_acc.get("ime_acc_id", "")),
                    "ime_id": self.__format_ids(ime_acc.get("ime_id", "")),
                    "margin_account": ime_acc.get("margin_account", ""),
                    "data": {
                        "name": {
                            "value": ime_acc.get("data", {}).get("ime_acc_name", {}).get("value", ""),
                            "page_number": ime_acc.get("data", {}).get("ime_acc_name", {}).get("page_number", None),
                        },
                        "number": {
                            "value": ime_acc.get("data", {}).get("ime_acc_number", {}).get("value", ""),
                            "page_number": ime_acc.get("data", {}).get("ime_acc_number", {}).get("page_number", None),
                        }
                    }
                } for ime_acc in data.get("ime_account", [])
            ],
            "ps_account": [
                {
                    "id": self.__format_ids(ps_acc.get("id", "")),
                    "imeacc_id": self.__format_ids(ps_acc.get("imeacc_id", "")),
                    "ps_id": self.__format_ids(ps_acc.get("ps_id", "")),
                    "data": {
                        "name": {
                            "value": ps_acc.get("data", {}).get("ps_plan_name", {}).get("value", ""),
                            "page_number": ps_acc.get("data", {}).get("ps_plan_name", {}).get("page_number", None),
                        },
                        "number": {
                            "value": ps_acc.get("data", {}).get("number", {}).get("value", ""),
                            "page_number": ps_acc.get("data", {}).get("number", {}).get("page_number", None),
                        }
                    }
                } for ps_acc in data.get("ps_account", [])
            ],
            "holding": [
                {
                    "id": self.__format_ids(holding.get("h_id", "")),
                    "ime_id": self.__format_ids(holding.get("ime_id", "")),
                    "imeacc_id": self.__format_ids(holding.get("ime_acc_id", "")),
                    "ps_id": self.__format_ids(holding.get("ps_id", "")),
                    "psacc_id": self.__format_ids(holding.get("ps_plan_id", "")),
                    "ht_id": self.__format_ids(holding.get("ht_id", "")),
                    "data": {
                        "name": { "value": holding.get("data", {}).get("name", {}).get("value", ""), "page_number": holding.get("data", {}).get("name", {}).get("page_number", None), },
                        "ticker": { "value": holding.get("data", {}).get("ticker", {}).get("value", ""), "page_number": holding.get("data", {}).get("ticker", {}).get("page_number", None), },
                        "class": { "value": holding.get("data", {}).get("class", {}).get("value", ""), "page_number": holding.get("data", {}).get("class", {}).get("page_number", None), },
                        "type": { "value": holding.get("data", {}).get("type", {}).get("value", ""), "page_number": holding.get("data", {}).get("type", {}).get("page_number", None), },
                        "exchange": { "value": holding.get("data", {}).get("exchange", {}).get("value", ""), "page_number": holding.get("data", {}).get("exchange", {}).get("page_number", None), },
                        "currency": { "value": holding.get("data", {}).get("currency", {}).get("value", ""), "page_number": holding.get("data", {}).get("currency", {}).get("page_number", None) },
                        "beginning_value": { "value": holding.get("data", {}).get("beginning_value", {}).get("value", ""), "page_number": holding.get("data", {}).get("beginning_value", {}).get("page_number", None), },
                        "ending_value": { "value": holding.get("data", {}).get("ending_value", {}).get("value", ""), "page_number": holding.get("data", {}).get("ending_value", {}).get("page_number", None), },
                        "security_unique_id": { "value": holding.get("data", {}).get("security_unique_id", {}).get("value", ""), "page_number": holding.get("data", {}).get("security_unique_id", {}).get("page_number", None), }
                    }
                } for holding in data.get("holding", [])
            ],
            "transaction": [
                {
                    "id": self.__format_ids(txn.get("t_id", "")),
                    "ime_id": self.__format_ids(txn.get("ime_id", "")),
                    "imeacc_id": self.__format_ids(txn.get("ime_acc_id", "")),
                    "ps_id": self.__format_ids(txn.get("ps_id", "")),
                    "psacc_id": self.__format_ids(txn.get("ps_plan_id", "")),
                    "tt_id": self.__format_ids(txn.get("tt_id", "")),
                    "data": {
                        "name": { "value": txn.get("data", {}).get("name", {}).get("value", ""), "page_number": txn.get("data", {}).get("name", {}).get("page_number", None), },
                        "transaction_date": { "value": txn.get("data", {}).get("transaction_date", {}).get("value", ""), "page_number": txn.get("data", {}).get("transaction_date", {}).get("page_number", None), },
                        "transaction_type": { "value": txn.get("data", {}).get("transaction_type", {}).get("value", ""), "page_number": txn.get("data", {}).get("transaction_type", {}).get("page_number", None), },
                        "ticker": { "value": txn.get("data", {}).get("ticker", {}).get("value", ""), "page_number": txn.get("data", {}).get("ticker", {}).get("page_number", None), },
                        "class": { "value": txn.get("data", {}).get("class", {}).get("value", ""), "page_number": txn.get("data", {}).get("class", {}).get("page_number", None), },
                        "type": { "value": txn.get("data", {}).get("type", {}).get("value", ""), "page_number": txn.get("data", {}).get("type", {}).get("page_number", None), },
                        "exchange": { "value": txn.get("data", {}).get("exchange", {}).get("value", ""), "page_number": txn.get("data", {}).get("exchange", {}).get("page_number", None), },
                        "currency": { "value": txn.get("data", {}).get("currency", {}).get("value", ""), "page_number": txn.get("data", {}).get("currency", {}).get("page_number", None) },
                        "investment_value": { "value": txn.get("data", {}).get("investment_value", {}).get("value", ""), "page_number": txn.get("data", {}).get("investment_value", {}).get("page_number", None), },
                        "security_unique_id": { "value": txn.get("data", {}).get("security_unique_id", {}).get("value", ""), "page_number": txn.get("data", {}).get("security_unique_id", {}).get("page_number", None), }
                    }
                } for txn in data.get("transaction", [])
            ],
        }
        return final_json

    def __format_ids(self, id_str: str):
        """Formats IDs to match the required pattern (e.g., 'IME001' -> 'IME_001')."""
        if not id_str or not isinstance(id_str, str) or len(id_str) < 2:
            return id_str
        
        p1 = re.findall(r'^[a-zA-Z]+', id_str)
        p1 = p1[0] if p1 else ""
        p2 = re.findall(r'\d+$', id_str)
        p2 = p2[0] if p2 else ""
        
        return f"{p1}_{p2}" if p1 and p2 else id_str