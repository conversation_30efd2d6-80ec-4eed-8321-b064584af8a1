import json
import pandas as pd
import re
from src.services.llm_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>
from src.services.prompt_manager import Prompt<PERSON>anager
from src.utils.logger import get_logger

LOGGER = get_logger("src.markdown_flow.post_processing_llm_revised")

class PostProcessingLLMRevised:
    """
    Implements an improved "Identify, Map, Extract" workflow for financial data extraction,
    handling both holdings and transactions.
    """

    def __init__(self, llm_handler: LLMHandler):
        """
        Initializes the handler for the revised flow.
        """
        self.llm_handler = llm_handler
        self.prompt_manager = PromptManager(llm_handler.prompts_dir)

    async def run(self, df):
        """
        Executes the revised post-processing pipeline.
        """
        # --- STEP 0: Prepare the document text ---
        LOGGER.info("Step 0: Preparing the document text in Markdown format.")
        markdown_full_text = self.format_in_markdown(df)
        # Create a dictionary of text per page to provide context for Agent 3
        pages_text = {
            page: f"### Page {page}\n" + group_df.to_string() 
            for page, group_df in df.groupby("page")
        }

        # --- AGENT 1: Identify data sections (Holdings and Transactions) ---
        LOGGER.info("Agent 1: Identifying holdings and transactions sections...")
        holding_sections = await self.identify_holding_sections(markdown_full_text)
        transaction_sections = await self.identify_transaction_sections(markdown_full_text)
        
        if not holding_sections:
            LOGGER.warning("No potential holding sections were identified.")
        if not transaction_sections:
            LOGGER.warning("No potential transaction sections were identified.")

        # --- AGENT 2: Extract all potential owner entities (done only once) ---
        LOGGER.info("Agent 2: Extracting all entities (IMEs, Accounts, PS, Plans)...")
        all_entities = await self.extract_all_entities(markdown_full_text)
        
        # --- Holdings Processing ---
        final_holdings = []
        holding_counter = 1
        LOGGER.info("Agents 3 & 4 (Holdings): Starting mapping and extraction of holdings...")
        for section in holding_sections:
            page_number = section.get("page_number")
            section_text = section.get("section_text")
            if not page_number or not section_text: continue

            owner = await self.map_holding_owner(section_text, pages_text.get(page_number, ""), page_number, all_entities)
            if not owner or not owner.get("ime_id"):
                LOGGER.warning(f"Could not map an owner for the holding section on page {page_number}.")
                continue
            
            extracted_data = await self.extract_holding_data(section_text, owner, page_number)
            for holding in extracted_data.get("holdings", []):
                holding.update(owner) # Add owner IDs to the holding
                holding["h_id"] = f"H{holding_counter:03d}"
                holding_counter += 1
                final_holdings.append(holding)
        LOGGER.info(f"Holdings processing finished. Total: {len(final_holdings)} holdings.")

        # --- Transaction Processing ---
        final_transactions = []
        transaction_counter = 1
        LOGGER.info("Agents 3 & 4 (Transactions): Starting mapping and extraction of transactions...")
        for section in transaction_sections:
            page_number = section.get("page_number")
            section_text = section.get("section_text")
            if not page_number or not section_text: continue

            owner = await self.map_transaction_owner(section_text, pages_text.get(page_number, ""), page_number, all_entities)
            if not owner or not owner.get("ime_id"):
                LOGGER.warning(f"Could not map an owner for the transaction section on page {page_number}.")
                continue

            extracted_data = await self.extract_transaction_data(section_text, owner, page_number)
            for transaction in extracted_data.get("transactions", []):
                transaction.update(owner) # Add owner IDs to the transaction
                transaction["t_id"] = f"T{transaction_counter:03d}"
                transaction_counter += 1
                final_transactions.append(transaction)
        LOGGER.info(f"Transaction processing finished. Total: {len(final_transactions)} transactions.")

        # --- Assemble the final JSON ---
        final_result = {
            "document_id": None,
            "investment_management_entity": all_entities.get("investment_management_entity", []),
            "date": all_entities.get("date", []),
            "plan_sponsor": all_entities.get("plan_sponsor", []),
            "ime_account": all_entities.get("ime_account", []),
            "ps_account": all_entities.get("ps_account", []),
            "holding": final_holdings,
            "transaction": final_transactions
        }
        
        return self.create_final_json(final_result)

    # --- Agent Implementation (Holdings) ---

    async def identify_holding_sections(self, text: str) -> list[dict]:
        """Agent 1: Scans the document and identifies the raw text of sections containing holding tables."""
        prompt = self.prompt_manager.get_prompt("holding_section_identifier_prompt", statement_text=text)
        result = await self.llm_handler.get_json_from_text_direct(prompt, default_response={})
        return result.get("holding_sections", [])

    async def map_holding_owner(self, section_text: str, page_text: str, page_number: int, entities: dict) -> dict:
        """Agent 3: Determines the owner of a holding section from a list of potential entities."""
        entities_list_str = self.format_entities_for_prompt(entities)
        prompt = self.prompt_manager.get_prompt("holding_ownership_mapper_prompt", page_number=page_number, page_text=page_text, section_text=section_text, entities_list_json=entities_list_str)
        result = await self.llm_handler.get_json_from_text_direct(prompt, default_response={})
        return result.get("owner")

    async def extract_holding_data(self, section_text: str, owner: dict, page_number: int) -> dict:
        """Agent 4: Extracts structured holding data from a verified section and owner."""
        prompt = self.prompt_manager.get_prompt("holdings_prompt_revised", owner_context=json.dumps(owner, indent=2), page_number=page_number, holdings_table_text=section_text)
        return await self.llm_handler.get_json_from_text_direct(prompt, default_response={})

    # --- Agent Implementation (Transactions) ---

    async def identify_transaction_sections(self, text: str) -> list[dict]:
        """Agent 1-T: Scans the document and identifies the raw text of sections containing transaction tables."""
        prompt = self.prompt_manager.get_prompt("transaction_section_identifier_prompt", statement_text=text)
        result = await self.llm_handler.get_json_from_text_direct(prompt, default_response={})
        return result.get("transaction_sections", [])

    async def map_transaction_owner(self, section_text: str, page_text: str, page_number: int, entities: dict) -> dict:
        """Agent 3-T: Determines the owner of a transaction section from a list of potential entities."""
        entities_list_str = self.format_entities_for_prompt(entities)
        prompt = self.prompt_manager.get_prompt("transaction_ownership_mapper_prompt", page_number=page_number, page_text=page_text, section_text=section_text, entities_list_json=entities_list_str)
        result = await self.llm_handler.get_json_from_text_direct(prompt, default_response={})
        return result.get("owner")

    async def extract_transaction_data(self, section_text: str, owner: dict, page_number: int) -> dict:
        """Agent 4-T: Extracts structured transaction data from a verified section and owner."""
        prompt = self.prompt_manager.get_prompt("transactions_prompt_revised", owner_context=json.dumps(owner, indent=2), page_number=page_number, transactions_table_text=section_text)
        return await self.llm_handler.get_json_from_text_direct(prompt, default_response={})

    # --- Agent 2 and Common Extraction Functions ---

    async def extract_all_entities(self, text: str) -> dict:
        """Agent 2: Extracts all entity types to create a "directory" of potential owners."""
        imes = await self.get_ime(text)
        dates, ime_accounts, plan_sponsors, ps_plans = [], [], [], []
        
        for entity in imes:
            dates.extend(await self.get_dates(text, entity))
            ime_accounts.extend(await self.get_ime_account_type(text, entity))
            sponsors = await self.get_ps(text, entity)
            plan_sponsors.extend(sponsors)
            for ps in sponsors:
                ps_plans.extend(await self.get_pse_plan(text, ps))
        
        ps_plans_mapped = await self.get_ps_imeacc_mapping(plan_sponsors, ime_accounts, ps_plans)
        
        return {
            "investment_management_entity": imes,
            "date": dates,
            "ime_account": ime_accounts,
            "plan_sponsor": plan_sponsors,
            "ps_account": ps_plans_mapped
        }

    async def get_ime(self, text: str) -> list[dict]:
        """Extracts investment management entities using a primary and fallback prompt."""
        ime_prompt = self.prompt_manager.get_prompt("investment_management_prompt", statement_text=text)
        ime_result = await self.llm_handler.get_json_from_text_direct(ime_prompt, default_response={})
        entities = ime_result.get("investment_management_entities", [])

        def has_valid_name(entity):
            return entity.get("data", {}).get("ime_name", {}).get("value")

        if not entities or not any(has_valid_name(e) for e in entities):
            LOGGER.warning("Primary IME extraction failed. Trying fallback 'elefante' prompt.")
            ime_elefante_prompt = self.prompt_manager.get_prompt("ime_elefante", statement_text=text)
            ime_result = await self.llm_handler.get_json_from_text_direct(ime_elefante_prompt, default_response={})
            entities = ime_result.get("investment_management_entities", [])

        for idx, entity in enumerate(entities, start=1):
            entity["ime_id"] = f"IME{idx:03d}"

        if not entities:
            LOGGER.warning("All extraction steps failed. Creating a default 'ELEFANTE ROSA' placeholder.")
            entities = [{"ime_id": "IME001", "data": {"ime_name": {"value": "ELEFANTE ROSA"}}}]
        
        return entities

    async def get_dates(self, text: str, entity: dict) -> list[dict]:
        """Extracts dates for a given entity."""
        ime_name_value = entity.get("data", {}).get("ime_name", {}).get("value", "")
        dates_prompt = self.prompt_manager.get_prompt("dates_prompt", statement_text=text, ime_id=entity.get("ime_id", ""), ime_name=ime_name_value)
        dates_result = await self.llm_handler.get_json_from_text_direct(dates_prompt, default_response={})
        dates_list = dates_result.get("date", [])
        for idx, date in enumerate(dates_list, start=1):
            date["id"] = f"DATE{idx:03d}"
        return dates_list

    async def get_ime_account_type(self, text: str, entity: dict) -> list[dict]:
        """Extracts IME account types for a given entity."""
        ime_name_value = entity.get("data", {}).get("ime_name", {}).get("value", "")
        prompt = self.prompt_manager.get_prompt("investment_management_account_type_prompt", statement_text=text, ime_id=entity.get("ime_id", ""), ime_name=ime_name_value)
        result = await self.llm_handler.get_json_from_text_direct(prompt, default_response={})
        accounts = result.get("accounts", [])
        for idx, acc in enumerate(accounts, start=1):
            if "data" in acc and "number" in acc["data"]:
                acc["data"]["ime_acc_number"] = acc["data"].pop("number")
            acc["ime_acc_id"] = f"IMEACC{idx:03d}"
        return accounts

    async def get_ps(self, text: str, entity: dict) -> list[dict]:
        """Extracts Plan Sponsors for a given entity."""
        prompt = self.prompt_manager.get_prompt("plan_sponsor_prompt", statement_text=text, entity_details=f"{entity.get('ime_id', '')}")
        result = await self.llm_handler.get_json_from_text_direct(prompt, default_response={})
        ps_list = result.get("plan_sponsor_entities", [])
        for idx, ps in enumerate(ps_list, start=1):
            ps["ps_id"] = f"PS{idx:03d}"
            ps["ime_id"] = entity.get("ime_id") # Ensure the relationship is maintained
        return ps_list

    async def get_pse_plan(self, text: str, ps: dict) -> list[dict]:
        """Extracts plans for a given Plan Sponsor."""
        ps_name = ps.get("data", {}).get("ps_name", {}).get("value", "")
        details = f"Plan Sponsor ID: {ps.get('ps_id', '')}\nPlan Sponsor Name: {ps_name}"
        prompt = self.prompt_manager.get_prompt("plan_sponsor_plan_prompt", statement_text=text, entity_details=details, ps_id=ps.get("ps_id"))
        result = await self.llm_handler.get_json_from_text_direct(prompt, default_response={})
        plans = result.get("ps_accounts", [])
        for idx, plan in enumerate(plans, start=1):
            plan["id"] = f"PSACC{idx:03d}"
            plan["ps_id"] = ps.get("ps_id") # Ensure the relationship is maintained
        return plans

    async def get_ps_imeacc_mapping(self, plan_sponsors: list, ime_accounts: list, ps_accounts: list):
        """Maps Plan Sponsor accounts to Investment Management Entity accounts."""
        if not ps_accounts or not ime_accounts:
            return ps_accounts
        prompt = self.prompt_manager.get_prompt("imeacc_id_prompt", ime_accounts=json.dumps(ime_accounts, indent=2), plan_sponsors=json.dumps(plan_sponsors, indent=2), ps_accounts=json.dumps(ps_accounts, indent=2))
        response = await self.llm_handler.get_json_from_text_direct(prompt, default_response={})
        mappings = response.get("account_mappings", [])
        
        valid_ime_acc_ids = {acc["ime_acc_id"] for acc in ime_accounts}
        validated_mapping_dict = { (m.get("id"), m.get("ps_id")): m.get("imeacc_id") for m in mappings if m.get("imeacc_id") in valid_ime_acc_ids }
        
        for plan in ps_accounts:
            key = (plan.get("id"), plan.get("ps_id"))
            plan["imeacc_id"] = validated_mapping_dict.get(key, None)
        return ps_accounts

    # --- Helper Functions ---

    def format_entities_for_prompt(self, entities: dict) -> str:
        """Creates a simplified and readable JSON representation of the entities for the mapping prompt."""
        formatted = {
            "IMEs": [{"ime_id": e.get("ime_id"), "name": e.get("data", {}).get("ime_name", {}).get("value")} for e in entities.get("investment_management_entity", [])],
            "IME_Accounts": [{"ime_acc_id": acc.get("ime_acc_id"), "name": acc.get("data", {}).get("ime_acc_name", {}).get("value"), "number": acc.get("data", {}).get("ime_acc_number", {}).get("value"), "ime_id": acc.get("ime_id")} for acc in entities.get("ime_account", [])],
            "Plan_Sponsors": [{"ps_id": ps.get("ps_id"), "name": ps.get("data", {}).get("ps_name", {}).get("value"), "ime_id": ps.get("ime_id")} for ps in entities.get("plan_sponsor", [])],
            "PS_Plans": [{"ps_plan_id": p.get("id"), "name": p.get("data", {}).get("ps_plan_name", {}).get("value"), "number": p.get("data", {}).get("number", {}).get("value"), "ps_id": p.get("ps_id")} for p in entities.get("ps_account", [])]
        }
        return json.dumps(formatted, indent=2)

    def format_in_markdown(self, df: pd.DataFrame):
        """Formats the dataframe content as markdown."""
        markdown_lines = []
        if df.empty:
            return ""
        for page, group in df.groupby("page"):
            markdown_lines.append(f"\n\n### Page {page}\n")
            for _, row in group.iterrows():
                content = row['content']
                if row["type"] == "paragraph":
                    markdown_lines.append(f"\n{content}")
                elif row["type"] == "table":
                    markdown_lines.append("\n#### TABLE START")
                    markdown_lines.append(content)
                    markdown_lines.append("#### TABLE END\n")
        return "\n".join(markdown_lines)

    def create_final_json(self, data: dict) -> dict:
        """
        Create the final JSON structure.
        
        Args:
            data: Input data to structure
            
        Returns:
            Structured JSON data
        """
        final_json = {
            "document_id": data.get("document_id", ""),
            "investment_management_entity": [
                {
                    "id": self.__format_ids(entity.get("ime_id", "")),
                    "data": {
                        "name": {
                            "value": entity.get("data", {}).get("ime_name", {}).get("value", ""),
                            "page_number": entity.get("data", {}).get("ime_name", {}).get("page_number", None),
                        },
                        "address": {
                            "value": entity.get("data", {}).get("address", {}).get("value", ""),
                            "page_number": entity.get("data", {}).get("address", {}).get("page_number", None),
                        },
                        "website": {
                            "value": entity.get("data", {}).get("website", {}).get("value", ""),
                            "page_number": entity.get("data", {}).get("website", {}).get("page_number", None),
                        },
                        "entity_unique_id": {
                            "value": entity.get("data", {}).get("entity_unique_id", {}).get("value", ""),
                            "page_number": entity.get("data", {}).get("entity_unique_id", {}).get("page_number", None),
                        }
                    }
                } for entity in data.get("investment_management_entity", [])
            ],
            "date": [
                {
                    "id": self.__format_ids(date.get("id", "")),
                    "ime_id": self.__format_ids(date.get("ime_id", "")),
                    "data": {
                        "start_date": {
                            "value": date.get("data", {}).get("start_date", {}).get("value", ""),
                            "page_number": date.get("data", {}).get("start_date", {}).get("page_number", None),
                        },
                        "end_date": {
                            "value": date.get("data", {}).get("end_date", {}).get("value", ""),
                            "page_number": date.get("data", {}).get("end_date", {}).get("page_number", None),
                        }
                    }
                } for date in data.get("date", [])
            ],
            "plan_sponsor": [
                {
                    "id": self.__format_ids(ps.get("ps_id", "")),
                    "ime_id": self.__format_ids(ps.get("ime_id", "")),
                    "data": {
                        "name": {
                            "value": ps.get("data", {}).get("ps_name", {}).get("value", ""),
                            "page_number": ps.get("data", {}).get("ps_name", {}).get("page_number", None),
                        },
                        "address": {
                            "value": ps.get("data", {}).get("address", {}).get("value", ""),
                            "page_number": ps.get("data", {}).get("address", {}).get("page_number", None),
                        },
                        "website": {
                            "value": ps.get("data", {}).get("website", {}).get("value", ""),
                            "page_number": ps.get("data", {}).get("website", {}).get("page_number", None),
                        },
                        "entity_unique_id": {
                            "value": ps.get("data", {}).get("entity_unique_id", {}).get("value", ""),
                            "page_number": ps.get("data", {}).get("entity_unique_id", {}).get("page_number", None),
                        }
                    }
                } for ps in data.get("plan_sponsor", [])
            ],
            "ime_account": [
                {
                    "id": self.__format_ids(ime_acc.get("ime_acc_id", "")),
                    "ime_id": self.__format_ids(ime_acc.get("ime_id", "")),
                    "margin_account": ime_acc.get("margin_account", ""),
                    "data": {
                        "name": {
                            "value": ime_acc.get("data", {}).get("ime_acc_name", {}).get("value", ""),
                            "page_number": ime_acc.get("data", {}).get("ime_acc_name", {}).get("page_number", None),
                        },
                        "number": {
                            "value": ime_acc.get("data", {}).get("ime_acc_number", {}).get("value", ""),
                            "page_number": ime_acc.get("data", {}).get("ime_acc_number", {}).get("page_number", None),
                        }
                    }
                } for ime_acc in data.get("ime_account", [])
            ],
            "ps_account": [
                {
                    "id": self.__format_ids(ps_acc.get("id", "")),
                    "imeacc_id": self.__format_ids(ps_acc.get("imeacc_id", "")),
                    "ps_id": self.__format_ids(ps_acc.get("ps_id", "")),
                    "data": {
                        "name": {
                            "value": ps_acc.get("data", {}).get("ps_plan_name", {}).get("value", ""),
                            "page_number": ps_acc.get("data", {}).get("ps_plan_name", {}).get("page_number", None),
                        },
                        "number": {
                            "value": ps_acc.get("data", {}).get("number", {}).get("value", ""),
                            "page_number": ps_acc.get("data", {}).get("number", {}).get("page_number", None),
                        }
                    }
                } for ps_acc in data.get("ps_account", [])
            ],
            "holding": [
                {
                    "id": self.__format_ids(holding.get("h_id", "")),
                    "ime_id": self.__format_ids(holding.get("ime_id", "")),
                    "imeacc_id": self.__format_ids(holding.get("ime_acc_id", "")),
                    "ps_id": self.__format_ids(holding.get("ps_id", "")),
                    "psacc_id": self.__format_ids(holding.get("ps_plan_id", "")),
                    "ht_id": self.__format_ids(holding.get("ht_id", "")),
                    "data": {
                        "name": {
                            "value": holding.get("data", {}).get("name", {}).get("value", ""),
                            "page_number": holding.get("data", {}).get("name", {}).get("page_number", None),
                        },
                        "ticker": {
                            "value": holding.get("data", {}).get("ticker", {}).get("value", ""),
                            "page_number": holding.get("data", {}).get("ticker", {}).get("page_number", None),
                        },
                        "class": {
                            "value": holding.get("data", {}).get("class", {}).get("value", ""),
                            "page_number": holding.get("data", {}).get("class", {}).get("page_number", None),
                        },
                        "type": {
                            "value": holding.get("data", {}).get("type", {}).get("value", ""),
                            "page_number": holding.get("data", {}).get("type", {}).get("page_number", None),
                        },
                        "exchange": {
                            "value": holding.get("data", {}).get("exchange", {}).get("value", ""),
                            "page_number": holding.get("data", {}).get("exchange", {}).get("page_number", None),
                        },
                        "currency": {
                            "value": holding.get("data", {}).get("currency", {}).get("value", ""),
                            "page_number": holding.get("data", {}).get("currency", {}).get("page_number", None)
                        },
                        "beginning_value": {
                            "value": holding.get("data", {}).get("beginning_value", {}).get("value", ""),
                            "page_number": holding.get("data", {}).get("beginning_value", {}).get("page_number", None),
                        },
                        "ending_value": {
                            "value": holding.get("data", {}).get("ending_value", {}).get("value", ""),
                            "page_number": holding.get("data", {}).get("ending_value", {}).get("page_number", None),
                        },
                        "security_unique_id": {
                            "value": holding.get("data", {}).get("security_unique_id", {}).get("value", ""),
                            "page_number": holding.get("data", {}).get("security_unique_id", {}).get("page_number", None),
                        }
                    }
                } for holding in data.get("holding", [])
            ],
            "transaction": [
                {
                    "id": self.__format_ids(txn.get("t_id", "")),
                    "ime_id": self.__format_ids(txn.get("ime_id", "")),
                    "imeacc_id": self.__format_ids(txn.get("ime_acc_id", "")),
                    "ps_id": self.__format_ids(txn.get("ps_id", "")),
                    "psacc_id": self.__format_ids(txn.get("ps_plan_id", "")),
                    "tt_id": self.__format_ids(txn.get("tt_id", "")),
                    "data": {
                        "name": {
                            "value": txn.get("data", {}).get("name", {}).get("value", ""),
                            "page_number": txn.get("data", {}).get("name", {}).get("page_number", None),
                        },
                        "transaction_date": {
                            "value": txn.get("data", {}).get("transaction_date", {}).get("value", ""),
                            "page_number": txn.get("data", {}).get("transaction_date", {}).get("page_number", None),
                        },
                        "transaction_type": {
                            "value": txn.get("data", {}).get("transaction_type", {}).get("value", ""),
                            "page_number": txn.get("data", {}).get("transaction_type", {}).get("page_number", None),
                        },
                        "ticker": {
                            "value": txn.get("data", {}).get("ticker", {}).get("value", ""),
                            "page_number": txn.get("data", {}).get("ticker", {}).get("page_number", None),
                        },
                        "class": {
                            "value": txn.get("data", {}).get("class", {}).get("value", ""),
                            "page_number": txn.get("data", {}).get("class", {}).get("page_number", None),
                        },
                        "type": {
                            "value": txn.get("data", {}).get("type", {}).get("value", ""),
                            "page_number": txn.get("data", {}).get("type", {}).get("page_number", None),
                        },
                        "exchange": {
                            "value": txn.get("data", {}).get("exchange", {}).get("value", ""),
                            "page_number": txn.get("data", {}).get("exchange", {}).get("page_number", None),
                        },
                        "currency": {
                            "value": txn.get("data", {}).get("currency", {}).get("value", ""),
                            "page_number": txn.get("data", {}).get("currency", {}).get("page_number", None)
                        },
                        "investment_value": {
                            "value": txn.get("data", {}).get("investment_value", {}).get("value", ""),
                            "page_number": txn.get("data", {}).get("investment_value", {}).get("page_number", None),
                        },
                        "security_unique_id": {
                            "value": txn.get("data", {}).get("security_unique_id", {}).get("value", ""),
                            "page_number": txn.get("data", {}).get("security_unique_id", {}).get("page_number", None),
                        }
                    }
                } for txn in data.get("transaction", [])
            ],
        }
        return final_json

    def __format_ids(self, id_str: str):
        """Formats the IDs to match the required pattern (e.g., 'IME001' -> 'IME_001')."""
        if not id_str or not isinstance(id_str, str) or len(id_str) < 2:
            return id_str
        p1 = "".join(re.findall(r'^[a-zA-Z]+', id_str))
        p2 = "".join(re.findall(r'\d+$', id_str))
        return f"{p1}_{p2}" if p1 and p2 else id_str