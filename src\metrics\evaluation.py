import pandas as pd
from scipy.optimize import linear_sum_assignment
from rapidfuzz import fuzz
from config.settings import config

def flatten_json(data):
    """
    Flattens the JSON structure by merging top-level entity fields 
    (like 'ht_id') with fields inside the 'data' object.
    """
    flat_data = {}
    for key, value in data.items():
        if isinstance(value, list) and value and isinstance(value[0], dict):
            processed_list = []
            for item in value:
                
                # Start with the fields inside the 'data' object
                merged_item = item.get('data', {}).copy()
                
                # Add all other top-level fields from the item, except 'data' itself
                for item_key, item_value in item.items():
                    if item_key != 'data':
                        merged_item[item_key] = item_value
                        
                processed_list.append(merged_item)
            flat_data[key] = processed_list
    return flat_data


def get_field_value(field_data):
    # Handles both nested values like {"value": "..."} and direct values like "HT_001"
    if isinstance(field_data, dict) and 'value' in field_data:
        val = field_data['value']

        if val is None or str(val).strip().lower() == "none":
            return ""
        
        return val
    
    if field_data is None or str(field_data).strip().lower() == "none":
        return ""
    
    return field_data

def calculate_string_dissimilarity(str1, str2):
    str1_safe = str(str1) if str1 is not None else ""
    str2_safe = str(str2) if str2 is not None else ""
    return 100 - fuzz.ratio(str1_safe.lower().strip(), str2_safe.lower().strip())

def get_optimal_matches(gt_items, llm_items, all_fields):
    """
    Creates a cost matrix based on string dissimilarity and uses the Hungarian algorithm
    to find the optimal matches between ground truth and LLM items.
    The rows of the matrix represent ground truth items, and the columns represent LLM items.
    Each value in the matrix represents the total dissimilarity between a ground truth item
    and an LLM item across all fields.
    """
    if not gt_items or not llm_items:
        return [], list(range(len(gt_items))), list(range(len(llm_items)))
    
    cost_matrix = [[0] * len(llm_items) for _ in range(len(gt_items))]
    for i, gt_item in enumerate(gt_items):
        for j, llm_item in enumerate(llm_items):
            total_dissimilarity = 0
            for field in all_fields:
                gt_value = get_field_value(gt_item.get(field))
                llm_value = get_field_value(llm_item.get(field))
                total_dissimilarity += calculate_string_dissimilarity(gt_value, llm_value)
            cost_matrix[i][j] = total_dissimilarity

    row_ind, col_ind = linear_sum_assignment(cost_matrix)
    matches = list(zip(row_ind, col_ind))
    unmatched_gt = list(set(range(len(gt_items))) - set(row_ind))
    unmatched_llm = list(set(range(len(llm_items))) - set(col_ind))
    return matches, unmatched_gt, unmatched_llm

def evaluate_fields(gt_data, llm_data, fuzzy_threshold=90):
    
    flat_gt = flatten_json(gt_data)
    flat_llm = flatten_json(llm_data)
    all_entity_types = set(flat_gt.keys()) | set(flat_llm.keys())
    metrics = {}

    for entity_type in all_entity_types:
        gt_items = flat_gt.get(entity_type, [])
        llm_items = flat_llm.get(entity_type, [])
        all_fields_in_entity = set()
        for item in gt_items + llm_items:
            all_fields_in_entity.update(item.keys())
        fields_to_evaluate = sorted([f for f in all_fields_in_entity if f not in config.EXCLUDED_FIELDS])

        # Initialize metrics
        for field in fields_to_evaluate:
            metric_key = f"{entity_type}.{field}"
            metrics[metric_key] = {
                'TP_Fuzzy': 0, 'TN_Fuzzy': 0, 'FP_Fuzzy': 0, 'FN_Fuzzy': 0,
                'TP_Exact': 0, 'TN_Exact': 0, 'FP_Exact': 0, 'FN_Exact': 0
            }

        matches, unmatched_gt_indices, unmatched_llm_indices = get_optimal_matches(gt_items, llm_items, fields_to_evaluate)

        for gt_idx, llm_idx in matches:
            gt_item = gt_items[gt_idx]
            llm_item = llm_items[llm_idx]
            for field in fields_to_evaluate:
                gt_value = get_field_value(gt_item.get(field))
                llm_value = get_field_value(llm_item.get(field))
                is_gt_present = str(gt_value).strip() != ""
                is_llm_present = str(llm_value).strip() != ""
                metric_key = f"{entity_type}.{field}"

                if is_gt_present and is_llm_present:
                    if str(gt_value).strip() == str(llm_value).strip():
                        metrics[metric_key]['TP_Exact'] += 1
                    else:
                        metrics[metric_key]['FP_Exact'] += 1
                    if fuzz.ratio(str(gt_value).lower().strip(), str(llm_value).lower().strip()) >= fuzzy_threshold:
                        metrics[metric_key]['TP_Fuzzy'] += 1
                    else:
                        metrics[metric_key]['FP_Fuzzy'] += 1
                elif is_gt_present and not is_llm_present:
                    metrics[metric_key]['FN_Exact'] += 1
                    metrics[metric_key]['FN_Fuzzy'] += 1
                elif not is_gt_present and is_llm_present:
                    metrics[metric_key]['FP_Exact'] += 1
                    metrics[metric_key]['FP_Fuzzy'] += 1
                else:
                    metrics[metric_key]['TN_Exact'] += 1
                    metrics[metric_key]['TN_Fuzzy'] += 1
                    pass
        for gt_idx in unmatched_gt_indices:
            gt_item = gt_items[gt_idx]
            for field in fields_to_evaluate:
                metric_key = f"{entity_type}.{field}"
                if str(get_field_value(gt_item.get(field, ""))).strip() != "":
                    metrics[metric_key]['FN_Exact'] += 1
                    metrics[metric_key]['FN_Fuzzy'] += 1
                else:
                    metrics[metric_key]['TN_Exact'] += 1
                    metrics[metric_key]['TN_Fuzzy'] += 1
                    pass

        for llm_idx in unmatched_llm_indices:
            llm_item = llm_items[llm_idx]
            for field in fields_to_evaluate:
                metric_key = f"{entity_type}.{field}"
                if str(get_field_value(llm_item.get(field, ""))).strip() != "":
                    metrics[metric_key]['FP_Exact'] += 1
                    metrics[metric_key]['FP_Fuzzy'] += 1
    return metrics

def calculate_final_metrics(df, include_tn_in_accuracy=False):
    """
    Calculates final metrics from a DataFrame of raw counts.

    Args:
        df (pd.DataFrame): DataFrame with TP, FP, FN, TN counts.
        include_tn_in_accuracy (bool): If True, use the standard accuracy formula.
                                       If False, use a formula that ignores TNs to avoid inflation.
    """
    df['Precision_Fuzzy'] = df['TP_Fuzzy'] / (df['TP_Fuzzy'] + df['FP_Fuzzy'])
    df['Recall_Fuzzy'] = df['TP_Fuzzy'] / (df['TP_Fuzzy'] + df['FN_Fuzzy'])
    
    df['Precision_Exact'] = df['TP_Exact'] / (df['TP_Exact'] + df['FP_Exact'])
    df['Recall_Exact'] = df['TP_Exact'] / (df['TP_Exact'] + df['FN_Exact'])

    if include_tn_in_accuracy:
        # Standard accuracy calculation: (TP + TN) / (TP + TN + FP + FN)
        total_fuzzy = df['TP_Fuzzy'] + df['TN_Fuzzy'] + df['FP_Fuzzy'] + df['FN_Fuzzy']
        df['Accuracy_Fuzzy'] = (df['TP_Fuzzy'] + df['TN_Fuzzy']) / total_fuzzy

        total_exact = df['TP_Exact'] + df['TN_Exact'] + df['FP_Exact'] + df['FN_Exact']
        df['Accuracy_Exact'] = (df['TP_Exact'] + df['TN_Exact']) / total_exact
    else:
        # Accuracy calculation ignoring TNs: (TP) / (TP + FP + FN)
        total_fuzzy = df['TP_Fuzzy'] + df['FP_Fuzzy'] + df['FN_Fuzzy']
        df['Accuracy_Fuzzy'] = df['TP_Fuzzy'] / total_fuzzy

        total_exact = df['TP_Exact'] + df['FP_Exact'] + df['FN_Exact']
        df['Accuracy_Exact'] = df['TP_Exact'] / total_exact

    return df