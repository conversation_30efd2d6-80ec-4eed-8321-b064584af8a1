import pandas as pd

# Make sure these imports point to your actual evaluation script location
from src.metrics.evaluation import evaluate_fields, calculate_final_metrics
from src.utils.file_handler import save_metrics_results

from src.utils.logger import get_logger
LOGGER = get_logger("src.metrics.metrics_calculator")

def calculate_and_save_metrics(gt_data_dict, llm_results_dict, fuzzy_threshold, run_name, model_name):
    """
    Calculates performance metrics by comparing ground truth and LLM results,
    saves them to a file, and returns a DataFrame for display.

    Args:
        gt_data_dict (dict): Dict with file_name as key and GT JSON data as value.
        llm_results_dict (dict): Dict with file_name as key and LLM JSON data as value.
        fuzzy_threshold (int): The similarity threshold (0-100).
        run_name (str): The unique name of the current run.
        model_name (str): The name of the LLM used.

    Returns:
        pd.DataFrame: A DataFrame containing detailed metrics per file and field.
    """
    LOGGER.info(f"Starting metrics calculation for run: {run_name}")
    
    all_files_metrics_dfs = []
    
    # Iterate through the files that were successfully processed by the LLM
    for file_name, llm_data in llm_results_dict.items():
        if file_name not in gt_data_dict:
            LOGGER.warning(f"Skipping metrics for {file_name}: No matching ground truth found.")
            continue

        gt_data = gt_data_dict[file_name]
        
        # This is the core calculation from your script
        file_metrics = evaluate_fields(gt_data, llm_data, fuzzy_threshold)

        if file_metrics:
            df_file = pd.DataFrame.from_dict(file_metrics, orient='index')
            df_file_final = calculate_final_metrics(df_file)
            df_file_final['file'] = file_name
            all_files_metrics_dfs.append(df_file_final)

    if not all_files_metrics_dfs:
        LOGGER.warning("No metrics could be calculated. The result set is empty.")
        return pd.DataFrame()

    # Concatenate all individual file metrics into one large DataFrame
    df_per_file = pd.concat(all_files_metrics_dfs).reset_index().rename(columns={'index': 'field'})
    
    # Add metadata to the DataFrame before saving
    df_to_save = df_per_file.copy()
    df_to_save['run_name'] = run_name
    df_to_save['model_used'] = model_name
    df_to_save['fuzzy_threshold_at_calculation'] = fuzzy_threshold
    
    # Save the results using our file handler
    save_metrics_results(df_to_save, run_name)
    
    LOGGER.info(f"Metrics calculation finished for run: {run_name}")
    
    # Return the raw metrics DataFrame for UI display
    return df_per_file