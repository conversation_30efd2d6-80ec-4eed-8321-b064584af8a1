from azure.identity import ClientSecretCredential
from azure.keyvault.secrets import SecretClient
import os

from src.utils.logger import get_logger
LOGGER = get_logger("src.services.key_vault_handler")

class KeyVaultHandler:
    """
    A handler for interacting with Azure Key Vault.
    This class connects to Azure Key Vault using client ID, tenant ID, and client secret.
    It provides methods to read secrets from the Key Vault.
    """

    def __init__(
            self, 
            vault_url: str = os.getenv("AZURE_KEY_VAULT_URL"), 
            client_id: str = os.getenv("AZURE_CLIENT_ID"), 
            tenant_id: str = os.getenv("AZURE_TENANT_ID"), 
            client_secret: str = os.getenv("AZURE_CLIENT_SECRET")
    ):
        """
        Initialize the KeyVaultHandler.

        Args:
            vault_url (str): The URL of the Azure Key Vault.
            client_id (str): The client ID for authentication.
            tenant_id (str): The tenant ID for authentication.
            client_secret (str): The client secret for authentication.
        """
        if not vault_url:
            raise ValueError("AZURE_KEY_VAULT_URL must be given or set in the environment variables.")
        if not client_id:
            raise ValueError("AZURE_CLIENT_ID must be given or set in the environment variables.")
        if not tenant_id:
            raise ValueError("AZURE_TENANT_ID must be given or set in the environment variables.")
        if not client_secret:
            raise ValueError("AZURE_CLIENT_SECRET must be given or set in the environment variables.")

        self.credential = ClientSecretCredential(
            tenant_id=tenant_id,
            client_id=client_id,
            client_secret=client_secret
        )
        self._client = SecretClient(vault_url=vault_url, credential=self.credential)

    def get_secret(self, secret_name: str) -> str:
        """
        Retrieve a secret from the Azure Key Vault.

        Args:
            secret_name (str): The name of the secret to retrieve.

        Returns:
            str: The value of the secret.
        """
        try:
            secret = self._client.get_secret(secret_name)
            return secret.value
        except Exception as e:
            LOGGER.warning(f"Failed to retrieve secret '{secret_name}': {e}")
            return None