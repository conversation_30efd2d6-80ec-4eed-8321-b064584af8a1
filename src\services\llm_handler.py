import json
from jinja2 import Environment, FileSystemLoader
from openai import BadRequest<PERSON>rror
from openai.types.chat.chat_completion import <PERSON><PERSON><PERSON><PERSON>pletion
from openai import AsyncAzureOpenAI
from tenacity import (
    retry,
    stop_after_attempt,
    wait_random_exponential,
)

from src.utils.logger import get_logger
LOGGER = get_logger("src.services.llm_handler")

class LLMHandler:
    """
    Handler for interactions with the LLM service.
    Provides methods for generating text and JSON responses from prompts.
    """
    
    def __init__(self, client, prompts_dir: str, system_prompt: str, llm_model_name: str):
        """
        Initialize the LLM handler.
        
        Args:
            client: Azure OpenAI client instance
            prompts_dir: Directory containing prompt templates
            system_prompt: System prompt to use for all interactions
        """
        self.client = client
        self.system_prompt = system_prompt
        self.prompts_dir = prompts_dir
        self.jinja_env = Environment(loader=FileSystemLoader(prompts_dir), trim_blocks=True, lstrip_blocks=True)
        self.llm_model_name = llm_model_name

    def _render_prompt(self, template_name: str, context: dict = None) -> str:
        """
        Load and render a Jinja2 template.
        
        Args:
            template_name: Name of the template file
            context: Context variables for rendering
            
        Returns:
            Rendered prompt string
        """
        template = self.jinja_env.get_template(template_name)
        return template.render(context or {})
    
    async def _call_api(self, user_prompt: str, base64_images: list = None, json_mode: bool = True, temperature: float = 0):
        """
        Make a call to the OpenAI API.
        
        Args:
            user_prompt: User prompt to send
            base64_images: List of base64 encoded images (optional)
            json_mode: Whether to request JSON response format
            temperature: Temperature parameter for generation
            
        Returns:
            A tuple containing (response_content, token_count) or (None, 0) if it fails.
        """
        messages = [{"role": "system", "content": self.system_prompt}]

        models_temp_1 = ["gpt-5", "gpt-5-mini"]

        if self.llm_model_name in models_temp_1:
            temperature = 1
        
        user_content = [{"type": "text", "text": user_prompt}]
        if base64_images:
            for b64_img in base64_images:
                user_content.append({
                    "type": "image_url",
                    "image_url": {"url": f"data:image/png;base64,{b64_img}"}
                })
        
        messages.append({"role": "user", "content": user_content})

        try:
            completion_params = {
                "model": self.llm_model_name,
                "messages": messages,
                "temperature": temperature,
            }
            if json_mode:
                completion_params["response_format"] = {"type": "json_object"}

            response = await self.completion_with_backoff(self.client, **completion_params)

            usage = response.usage if hasattr(response, 'usage') else None
            prompt_tokens = usage.prompt_tokens if usage else 0
            completion_tokens = usage.completion_tokens if usage else 0
            tokens = prompt_tokens + completion_tokens

            return response.choices[0].message.content, tokens
        except BadRequestError as e:
            LOGGER.error(f"OpenAI API BadRequestError (400): {e.response.text}")
            LOGGER.error(f"Request parameters that caused the error: {completion_params}")
            return None, 0
        except Exception as e:
            LOGGER.error(f"Error calling OpenAI API: {e}")
            return None, 0
    
    async def get_json_from_images(self, template_name: str, context: dict, base64_images: list, default_response: dict = None):
        """
        Process images to extract a JSON object.
        Returns a dictionary with "result" and "tokens".
        """
        prompt = self._render_prompt(template_name, context)
        raw_response, tokens = await self._call_api(prompt, base64_images=base64_images, json_mode=True)

        output = {
            "result": default_response or {},
            "tokens": tokens or 0
        }
        
        if not raw_response:
            LOGGER.warning(f"No response from LLM for prompt {template_name}. Returning default.")
            return output
            
        try:
            output["result"] = json.loads(raw_response)
            return output
        except json.JSONDecodeError:
            LOGGER.error(f"Failed to decode JSON from LLM for prompt {template_name}. Raw response: {raw_response}")
            return output

    async def get_markdown_from_images(self, template_name: str, context: dict, base64_images: list):
        """
        Process images to extract markdown text. Returns a tuple (text, tokens).
        """
        prompt = self._render_prompt(template_name, context)
        response, tokens = await self._call_api(prompt, base64_images=base64_images, json_mode=False)
        return response or "", tokens or 0
    
    async def get_json_from_text(self, template_name: str, context: dict, default_response: dict = None):
        """
        Process text to extract a JSON object.
        Returns a dictionary with "result" and "tokens".
        """
        prompt = self._render_prompt(template_name, context)
        raw_response, tokens = await self._call_api(prompt, json_mode=True)

        output = {
            "result": default_response or {},
            "tokens": tokens or 0
        }

        if not raw_response:
            LOGGER.warning(f"No response from LLM for prompt {template_name}. Returning default.")
            return output
            
        try:
            output["result"] = json.loads(raw_response)
            return output
        except json.JSONDecodeError:
            LOGGER.error(f"Failed to decode JSON from LLM for prompt {template_name}. Raw response: {raw_response}")
            return output
    
    async def get_text_response(self, user_prompt: str, model: str = None, temperature: float = 0):
        """
        Send a text prompt and get a text response.
        
        Args:
            user_prompt: User prompt to send
            temperature: Temperature parameter
            
        Returns:
            A tuple (response_text, token_count) or (None, 0) if it fails.
        """
        model_names_temp_1 = ["gpt-5", "gpt-5-mini"]

        if self.llm_model_name in model_names_temp_1:
            temperature = 1
        else:
            temperature = 0

        messages = [
            {"role": "system", "content": self.system_prompt},
            {"role": "user", "content": user_prompt}
        ]
        try:
            response = await self.completion_with_backoff(
                self.client,
                model=self.llm_model_name,
                messages=messages,
                temperature=temperature
            )
            
            usage = response.usage if hasattr(response, 'usage') else None
            prompt_tokens = usage.prompt_tokens if usage else 0
            completion_tokens = usage.completion_tokens if usage else 0
            tokens = prompt_tokens + completion_tokens
            
            content = response.choices[0].message.content
            
            return content, tokens
        except Exception as e:
            LOGGER.error(f"Error calling OpenAI API: {e}")
            return None, 0
    
    async def get_json_from_text_direct(self, user_prompt: str, default_response: dict = None) -> dict:
        """
        Send a text prompt and get a JSON response.
        NOTE: This method does NOT return token counts. Use get_json_from_text for that.
        
        Args:
            user_prompt: User prompt to send
            default_response: Default response if processing fails
            
        Returns:
            JSON response or default response
        """
        model_names_temp_1 = ["gpt-5", "gpt-5-mini"]

        if self.llm_model_name in model_names_temp_1:
            temperature = 1
        else:
            temperature = 0

        messages = [
            {"role": "system", "content": self.system_prompt},
            {"role": "user", "content": user_prompt}
        ]
        try:
            response = await self.completion_with_backoff(
                self.client,
                model=self.llm_model_name,
                messages=messages,
                temperature=temperature,
                response_format={"type": "json_object"}
            )
            raw_response = response.choices[0].message.content
            return json.loads(raw_response)
        except Exception as e:
            LOGGER.error(f"Error calling OpenAI API: {e}")
            return default_response or {}
    
    @staticmethod
    @retry(wait=wait_random_exponential(min=1, max=2), stop=stop_after_attempt(3))
    async def completion_with_backoff(client: AsyncAzureOpenAI, **kwargs) -> ChatCompletion:
        """
        Make a completion request with exponential backoff retry.
        
        Args:
            client: OpenAI client instance
            **kwargs: Arguments for the completion request
            
        Returns:
            Chat completion response
        """
        return await client.chat.completions.create(**kwargs)