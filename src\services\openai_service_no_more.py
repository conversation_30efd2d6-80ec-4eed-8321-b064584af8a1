from openai import AzureOpenAI
from openai import NOT_GIVEN
from openai.types.chat.chat_completion import Cha<PERSON><PERSON><PERSON>ple<PERSON>
from config.settings import config
import json
import logging
from tenacity import (
    retry,
    stop_after_attempt,
    wait_random_exponential,
)

logger = logging.getLogger(__name__)

class OpenAIService:
    """
    Service for interacting with OpenAI API.
    """
    
    def __init__(self):
        """
        Initialize the OpenAI service.
        """
        self.client_openai = AzureOpenAI(
            api_key=config.openai_api_key,
            azure_endpoint=config.openai_endpoint,
            api_version=config.api_version,
        )
        self.model = {
            "standard": config.default_model,
            "enhanced": config.fast_model
        }
        self.system_instructions = "You are a helpful AI assistant that extracts information from financial documents."
    
    def openai_requests(self, prompt: str, response_format=NOT_GIVEN, model: str = "standard") -> str:
        """
        Make a request to OpenAI API.
        
        Args:
            prompt: User prompt
            response_format: Response format (JSON or text)
            model: Model variant to use
            
        Returns:
            Response content or empty string on error
        """
        try:
            model_to_use = self.model[model]
            
            response = self.completion_with_backoff(
                client=self.client_openai,
                model=model_to_use,
                messages=[
                    {"role": "system", "content": self.system_instructions},
                    {"role": "user", "content": prompt}
                ],
                response_format=response_format
            )
            return response.choices[0].message.content
        except Exception as e:
            logger.error(e)
            return ""
    
    def openai_requests_tools(self, prompt: str, tool: dict = None) -> list[dict]:
        """
        Make a request to OpenAI API with tools.
        
        Args:
            prompt: User prompt
            tool: Tool definition
            
        Returns:
            List of tool arguments or empty list on error
        """
        try:
            response = self.completion_with_backoff(
                client=self.client_openai,
                model=self.model["standard"],
                messages=[
                    {"role": "system", "content": self.system_instructions},
                    {"role": "user", "content": prompt}
                ],
                tools=[tool],
            )
            tool_calls = response.choices[0].message.tool_calls
            arguments = []
            for tool_call in tool_calls:
                args_str = tool_call.function.arguments
                args = json.loads(args_str)
                arguments.append(args)
            return arguments
        
        except Exception as e:
            logger.error(e)
            return []
    
    @staticmethod
    @retry(wait=wait_random_exponential(min=1, max=2), stop=stop_after_attempt(3))
    def completion_with_backoff(client: AzureOpenAI, **kwargs) -> ChatCompletion:
        """
        Make a completion request with exponential backoff retry.
        
        Args:
            client: OpenAI client instance
            **kwargs: Arguments for the completion request
            
        Returns:
            Chat completion response
        """
        return client.chat.completions.create(**kwargs)