import os
from jinja2 import Environment, FileSystemLoader, TemplateError

class PromptManager:
    """
    Manager for loading and rendering prompt templates.
    """
    
    def __init__(self, prompts_dir=None):
        """
        Initialize the prompt manager.
        
        Args:
            prompts_dir: Directory containing prompt templates (default: ../prompts)
        """
        if prompts_dir is None:
            # Default prompts directory
            base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            prompts_dir = os.path.join(base_dir, "prompts")
        self.env = Environment(loader=FileSystemLoader(prompts_dir))
    
    def get_prompt(self, template_name: str, **kwargs) -> str:
        """
        Get a rendered prompt template.
        
        Args:
            template_name: Name of the template file (without extension)
            **kwargs: Context variables for rendering
            
        Returns:
            Rendered prompt string
        """
        template = self.env.get_template(f"{template_name}.j2")
        try:
            return template.render(**kwargs)
        except TemplateError as e:
            raise TemplateError(f"Error rendering template: {e}")