import cv2
import fitz  # PyMuPDF
import numpy as np
import matplotlib.pyplot as plt
import os

from src.utils.logger import get_logger
LOGGER = get_logger("src.utils.bbox_viewer")

def find_fields_with_boxes_pdf(data_blob, target_page_number):
    """
    Recursively finds all fields in a JSON blob that have bounding regions
    on a specific page number.
    """
    if isinstance(data_blob, dict):
        # Check if this dictionary represents a field with a valid bounding box on the target page
        if ('bounding_regions' in data_blob and
            data_blob.get('page_number') == target_page_number and
            data_blob.get('bounding_regions') is not None and
            len(data_blob['bounding_regions']) >= 3):
            yield data_blob
        # Recurse into the values of the dictionary
        for value in data_blob.values():
            yield from find_fields_with_boxes_pdf(value, target_page_number)
    elif isinstance(data_blob, list):
        # Recurse into each item in the list
        for item in data_blob:
            yield from find_fields_with_boxes_pdf(item, target_page_number)

def plot_boxes_on_pdf_page(page_number, json_data, doc_or_path):
    """
    Renders a specific page of a PDF and draws all bounding boxes.
    Accepts either a file path (string) or an already opened fitz.Document object.
    """
    DPI = 200
    fig = None
    doc = None
    try:
        # If a string (path) is passed, open the document.
        # If a fitz.Document object is passed, use it directly.
        if isinstance(doc_or_path, str):
            doc = fitz.open(doc_or_path)
            title = f"Visualization for Page {page_number} of {os.path.basename(doc_or_path)}"
        else:
            doc = doc_or_path
            title = f"Visualization for Page {page_number} (from memory)"

        page = doc.load_page(page_number - 1)
        pix = page.get_pixmap(dpi=DPI)
        img_data = np.frombuffer(pix.samples, dtype=np.uint8).reshape(pix.height, pix.width, 3)
        page_image_cv2 = cv2.cvtColor(img_data, cv2.COLOR_RGB2BGR)

    except Exception as e:
        LOGGER.error(f"Error processing PDF with PyMuPDF: {e}")
        return None
    finally:
        # Close the document only if we opened it here (from a path)
        if isinstance(doc_or_path, str) and doc:
            doc.close()

    colors = [(255, 0, 0), (0, 255, 0), (0, 0, 255), (255, 255, 0), (0, 255, 255), (255, 0, 255)]
    
    for i, field_data in enumerate(find_fields_with_boxes_pdf(json_data, page_number)):
        region = field_data['bounding_regions']
        # The coordinates might be normalized or absolute. Let's check.
        # Assuming normalized for PDFs as per your original code.
        x0 = int(region[0]['x'] * DPI)
        y0 = int(region[0]['y'] * DPI)
        x1 = int(region[2]['x'] * DPI)
        y1 = int(region[2]['y'] * DPI)
        
        cv2.rectangle(page_image_cv2, (x0, y0), (x1, y1), colors[i % len(colors)], 2)

    fig, ax = plt.subplots()
    ax.imshow(cv2.cvtColor(page_image_cv2, cv2.COLOR_BGR2RGB))
    ax.set_title(title)
    ax.axis('off')
    return fig


def find_all_fields_with_boxes_img(data_blob):
    """
    Recursively finds all fields in a JSON blob that have bounding regions,
    regardless of page number (for single-page images).
    """
    if isinstance(data_blob, dict):
        if 'bounding_regions' in data_blob and data_blob.get('bounding_regions') is not None and len(data_blob['bounding_regions']) >= 3:
            yield data_blob
        for value in data_blob.values():
            yield from find_all_fields_with_boxes_img(value)
    elif isinstance(data_blob, list):
        for item in data_blob:
            yield from find_all_fields_with_boxes_img(item)

def plot_boxes_on_image(json_data, image_or_path):
    """
    Renders an image and draws all bounding boxes.
    Accepts either a file path (string) or image content as bytes.
    """
    fig = None
    try:
        # If a string (path) is passed, read the image from the file.
        # If bytes are passed, read the image from memory.
        if isinstance(image_or_path, str):
            image_cv = cv2.imread(image_or_path)
            title = f"Visualization for Image: {os.path.basename(image_or_path)}"
        else:
            # Convert bytes to a numpy array for OpenCV
            image_np = np.frombuffer(image_or_path, np.uint8)
            image_cv = cv2.imdecode(image_np, cv2.IMREAD_COLOR)
            title = "Visualization for Image (from memory)"
        
        if image_cv is None:
            LOGGER.error("Could not decode image data.")
            return None

    except Exception as e:
        LOGGER.error(f"Error occurred while reading image: {e}")
        return None

    colors = [(255, 0, 0), (0, 255, 0), (0, 0, 255), (255, 255, 0), (0, 255, 255), (255, 0, 255)]
    
    for i, field_data in enumerate(find_all_fields_with_boxes_img(json_data)):
        region = field_data.get('bounding_regions')
        # Assuming absolute pixel coordinates for images as per your original code
        x0 = int(region[0]['x'])
        y0 =  int(region[0]['y'])
        x1 = int(region[2]['x'])
        y1 = int(region[2]['y'])
        cv2.rectangle(image_cv, (x0, y0), (x1, y1), colors[i % len(colors)], 3)

    fig, ax = plt.subplots()
    ax.imshow(cv2.cvtColor(image_cv, cv2.COLOR_BGR2RGB))
    ax.set_title(title)
    ax.axis('off')
    return fig