from sklearn.cluster import KMeans
from sklearn.metrics import silhouette_score
import numpy as np


def calculate_segments(data, max_k:int=5):
    """Calculate the number of segments in the data using KMeans clustering and Silhouette Score
    """
    if len(data) <= 2:
        return {
            "segment": 1,
            "labels": [0 for _ in data]
        }
    
    best_k = 0
    best_score = -1
    labels = {}
    data_r = data.reshape(-1,1)

    for k in range(2,5):
        if len(data_r)>k:
            kmeans = KMeans(n_clusters=k, random_state=42).fit(data_r)
            
            sorted_indices = np.argsort(kmeans.cluster_centers_.flatten())
            label_map = {sorted_indices[i]: i for i in range(k)}
            sorted_labels = np.array([label_map[label] for label in kmeans.labels_])

            labels[k] = sorted_labels
            score = silhouette_score(data_r, sorted_labels)

            if score > best_score:
                best_score = score
                best_k = k

    return {
        "segment": best_k,
        "labels": labels[best_k]
    }
