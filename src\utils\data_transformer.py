def reindex_list_ids(list_of_dicts, id_field_name, prefix):
    """
    Re-indexes a specific ID field in a list of dictionaries.
    The new IDs are sequential integers (1, 2, 3, ...).
    Original IDs that are the same will receive the same new integer ID.
    The order of appearance determines the assignment of new IDs.

    Args:
        list_of_dicts (list): A list of dictionaries.
        id_field_name (str): The name of the key in the dictionaries
                             whose value needs to be re-indexed.
        prefix (str): The prefix to add to the new ID.
    """
    original_to_new_id_map = {}
    current_new_id = 1
    for item_dict in list_of_dicts:
        if id_field_name in item_dict:
            original_id = item_dict[id_field_name]
            if original_id not in original_to_new_id_map:
                new_id = f"{prefix}_{current_new_id:03d}"
                original_to_new_id_map[original_id] = new_id
                current_new_id += 1
            item_dict[id_field_name] = original_to_new_id_map[original_id]

def transform_json_structure(input_json_data):
    output_dict = {}

    # Define configurations for each entity type based on the TARGET structure.
    # This specifies how to map input fields to the new structure.
    entity_configs = {
        'date': {
            'id_input_key': 'date_id', 
            'output_id_key': 'id',     
            'data_field_mapping': {   
                'start_date': 'start_date',
                'end_date': 'end_date'
            }
        },
        'investment_management_entity': {
            'id_input_key': 'id',
            'output_id_key': 'id',
            'top_level_mapping': {"date_id": "date_id"},
            'data_field_mapping': {
                'name': 'name',
                'address': 'address',
                'website': 'website',
                'entity_unique_id': 'entity_unique_id'
            }
        },
        'ime_account': {
            'id_input_key': 'id',
            'output_id_key': 'id',
            'top_level_mapping': {'ime_id': 'ime_id', 'margin_account': 'margin_account'},
            'data_field_mapping': {
                'name': 'name',
                'number': 'number'
            }
        },
        'ps_account': {
            'id_input_key': 'id',
            'output_id_key': 'id',
            'top_level_mapping': {'imeacc_id': 'imeacc_id', 'ps_id': 'ps_id'},
            'data_field_mapping': {
                'name': 'name',
                'number': 'number'
            }
        },
        'plan_sponsor': {
            'id_input_key': 'id',
            'output_id_key': 'id',
            'top_level_mapping': {'ime_id': 'ime_id'},
            'data_field_mapping': {
                'name': 'name',
                'address': 'address',
                'website': 'website',
                'entity_unique_id': 'entity_unique_id'
            }
        },
        'holding': {
            'output_id_key': 'id',
            'top_level_mapping': {
                'ime_id': 'ime_id', 'imeacc_id': 'imeacc_id',
                'ps_id': 'ps_id', 'psacc_id': 'psacc_id', 'ht_id': 'ht_id'
            },
            'data_field_mapping': {
                'name': 'name', 'ticker': 'ticker', 'class': 'class', 'type': 'type',
                'exchange': 'exchange', 'currency': 'currency',
                'beginning_value': 'beginning_value', 'ending_value': 'ending_value',
                'security_unique_id': 'security_unique_id'
            }
        },
        'transaction': {
            'output_id_key': 'id',
            'top_level_mapping': {
                'ime_id': 'ime_id', 'imeacc_id': 'imeacc_id',
                'ps_id': 'ps_id', 'psacc_id': 'psacc_id', 'tt_id': 'tt_id'
            },
            'data_field_mapping': {
                'name': 'name', 'transaction_date': 'transaction_date',
                'transaction_type': 'transaction_type', 'ticker': 'ticker',
                'class': 'class', 'type': 'type', 'exchange': 'exchange',
                'currency': 'currency', 'investment_value': 'investment_value',
                'security_unique_id': 'security_unique_id'
            }
        }
    }

    target_keys_ordered = [
        "investment_management_entity", "date", "plan_sponsor",
        "ime_account", "ps_account", "holding", "transaction"
    ]
    for t_key in target_keys_ordered:
        output_dict[t_key] = []

    # Process each entity type from the input JSON
    for entity_key, item_list in input_json_data.items():
        if entity_key not in entity_configs:
            print(f"Warning: Entity key '{entity_key}' from input is not defined in configurations. Skipping.")
            continue

        config = entity_configs[entity_key]

        for idx, input_item in enumerate(item_list):
            transformed_item = {}
            item_page_number = input_item.get('page_number', [])

            # 1. Handle the main 'id' for the item
            output_id_key_name = config.get('output_id_key', 'id')
            if 'id_input_key' in config:
                id_value = input_item.get(config['id_input_key'])
                transformed_item[output_id_key_name] = id_value
            elif entity_key == 'holding':
                transformed_item[output_id_key_name] = f"H_{idx + 1:03d}"
            elif entity_key == 'transaction':
                transformed_item[output_id_key_name] = f"T_{idx + 1:03d}"

            # 2. Handle other top-level fields
            for out_tl_key, in_tl_key in config.get('top_level_mapping', {}).items():
                if in_tl_key in input_item:
                    transformed_item[out_tl_key] = input_item[in_tl_key]
            
            # 3. Handle fields to be nested under 'data'
            data_dict = {}
            if 'data_field_mapping' in config:
                for out_data_key, in_data_key in config['data_field_mapping'].items():
                    # Get value from input_item; defaults to None if key is missing or value is explicitly None
                    value = input_item.get(in_data_key) 
                    data_dict[out_data_key] = {'value': value, 'page_number': item_page_number}
                transformed_item['data'] = data_dict
            
            output_dict[entity_key].append(transformed_item)
            
    return output_dict