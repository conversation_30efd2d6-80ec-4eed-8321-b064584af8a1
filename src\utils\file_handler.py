import os
import json
import pandas as pd
import requests
import datetime
from azure.storage.blob import BlobServiceClient, generate_blob_sas, BlobSasPermissions
from config.settings import config

from src.utils.logger import get_logger
LOGGER = get_logger("src.utils.file_handler")


CONTAINER_NAME = config.container_name

PATHS = {
    "raw": "Raw Document",
    "ground_truth": "Ground Truth",
    "extracted": "Extracted",
    "metrics": "Performance Metrics",
    "runs": "Runs"
}

blob_service_client = None
container_client = None

if config.blob_storage_connection_string:
    try:
        blob_service_client = BlobServiceClient.from_connection_string(config.blob_storage_connection_string,connection_timeout=600)
        container_client = blob_service_client.get_container_client(CONTAINER_NAME)
        if not container_client.exists():
            container_client.create_container()
            LOGGER.info(f"Container '{CONTAINER_NAME}' created.")
    except Exception as e:
        LOGGER.error(f"Failed to connect to Azure Blob Storage: {e}")
else:
    LOGGER.error("AZURE_STORAGE_CONNECTION_STRING environment variable not set.")

def _get_clean_filename(file_name, new_extension):
    """Removes old extension and adds a new one."""
    base_name = os.path.splitext(file_name)[0]
    return f"{base_name}{new_extension}"

def save_ground_truth(file_name, gt_data):
    """
    Saves the ground truth dictionary as a JSON file in Azure Blob Storage,
    after removing the document_url key to prevent storing obsolete URLs.
    """
    if not container_client: return
    try:
        if 'document_url' in gt_data:
            gt_data.pop('document_url')

        json_filename = _get_clean_filename(file_name, ".json")
        blob_name = f"{PATHS['ground_truth']}/{json_filename}"
        blob_client = container_client.get_blob_client(blob_name)
        
        # Convert the dictionary to a JSON string and upload it.
        blob_client.upload_blob(json.dumps(gt_data, indent=4), overwrite=True)
        LOGGER.info(f"Saved ground truth for {file_name} to blob: {blob_name}")
    except Exception as e:
        LOGGER.error(f"Failed to save ground truth blob for {file_name}: {e}")

def get_ground_truth_json(file_name: str) -> dict | None:
    """
    Downloads and parses a Ground Truth JSON file from Azure Blob Storage.

    Args:
        file_name: The name of the original document (e.g., my_doc.pdf).

    Returns:
        The parsed JSON data as a dictionary, or None if it fails.
    """
    if not container_client: return None
    try:
        json_filename = _get_clean_filename(file_name, ".json")
        blob_name = f"{PATHS['ground_truth']}/{json_filename}"
        blob_client = container_client.get_blob_client(blob_name)
        if blob_client.exists():
            downloader = blob_client.download_blob()
            return json.loads(downloader.readall())
        else:
            LOGGER.warning(f"Ground Truth blob not found: {blob_name}")
            return None
    except Exception as e:
        LOGGER.error(f"Failed to download or parse Ground Truth blob {file_name}: {e}")
        return None

def save_metrics_results(metrics_df, run_name):
    """Saves the metrics DataFrame to a CSV file in Azure Blob Storage."""
    if not container_client: return
    try:
        csv_filename = f"metrics_{run_name}.csv"
        blob_name = f"{PATHS['metrics']}/{csv_filename}"
        blob_client = container_client.get_blob_client(blob_name)
        
        csv_data = metrics_df.to_csv(index=False)
        blob_client.upload_blob(csv_data, overwrite=True)
        LOGGER.info(f"Successfully saved metrics to blob: {blob_name}")
    except Exception as e:
        LOGGER.error(f"Failed to save metrics blob for run {run_name}: {e}")

def save_llm_extraction(file_name, llm_data, run_name):
    """
    Saves the LLM extraction dictionary as a JSON file in a run-specific folder 
    in Azure Blob Storage.
    """
    if not container_client: return
    try:
        json_filename = _get_clean_filename(file_name, ".json")
        blob_name = f"{PATHS['extracted']}/{run_name}/{json_filename}"
        blob_client = container_client.get_blob_client(blob_name)
        
        blob_client.upload_blob(json.dumps(llm_data, indent=4), overwrite=True)
        LOGGER.info(f"Saved LLM extraction for {file_name} from run {run_name} to blob: {blob_name}")
    except Exception as e:
        LOGGER.error(f"Failed to save LLM extraction blob for {file_name} in run {run_name}: {e}")

def save_raw_document(file_name, file_url):
    """Downloads a document from a URL and saves it to Azure Blob Storage."""
    if not container_client: return
    try:
        LOGGER.info(f"Downloading raw document from {file_url} to stream to Azure...")
        response = requests.get(file_url, stream=True)
        response.raise_for_status()
        
        blob_name = f"{PATHS['raw']}/{file_name}"
        blob_client = container_client.get_blob_client(blob_name)
        
        blob_client.upload_blob(response.iter_content(chunk_size=8192), overwrite=True)
        LOGGER.info(f"Saved raw document to blob: {blob_name}")
    except requests.exceptions.RequestException as e:
        LOGGER.error(f"Failed to download/upload raw document {file_name} from {file_url}: {e}")

def save_run_metadata(run_info):
    """Saves the metadata of a run as a JSON file in Azure Blob Storage."""
    if not container_client: return
    try:
        run_name = run_info.get("Name", f"run_{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}")
        json_filename = f"{run_name}.json"
        blob_name = f"{PATHS['runs']}/{json_filename}"
        blob_client = container_client.get_blob_client(blob_name)
        
        blob_client.upload_blob(json.dumps(run_info, indent=4), overwrite=True)
        LOGGER.info(f"Saved run metadata to blob: {blob_name}")
    except Exception as e:
        LOGGER.error(f"Failed to save run metadata blob: {e}")

def load_run_metadata_from_disk():
    """Scans the 'Runs' virtual folder in Azure, loads each run's JSON metadata, and returns a list."""
    if not container_client: return []
    LOGGER.info("Loading run metadata from Azure Blob Storage.")
    runs_list = []
    
    blob_list = container_client.list_blobs(name_starts_with=f"{PATHS['runs']}/")
    sorted_blobs = sorted(blob_list, key=lambda b: b.name, reverse=True)

    for blob in sorted_blobs:
        if blob.name.endswith(".json"):
            try:
                blob_client = container_client.get_blob_client(blob.name)
                downloader = blob_client.download_blob()
                run_data = json.loads(downloader.readall())
                runs_list.append(run_data)
            except Exception as e:
                LOGGER.error(f"Failed to load or parse run blob {blob.name}: {e}")
    
    return runs_list

def load_golden_set_documents():
    """
    Scans the 'Raw Document' folder in Azure Blob Storage, generates a SAS URL for each document,
    and returns a DataFrame with file names and their corresponding temporary URLs.
    This is the correct way to load documents for processing.
    """
    if not container_client:
        return pd.DataFrame()
    
    LOGGER.info("Loading documents from Golden Set by listing raw files and generating SAS URLs.")
    documents = []
    
    blob_list = container_client.list_blobs(name_starts_with=f"{PATHS['raw']}/")

    for blob in blob_list:
        try:
            file_name = os.path.basename(blob.name)
            if not file_name: continue

            blob_client = container_client.get_blob_client(blob)
            
            sas_token = generate_blob_sas(
                account_name=blob_service_client.account_name,
                container_name=container_client.container_name,
                blob_name=blob.name,
                account_key=blob_service_client.credential.account_key,
                permission=BlobSasPermissions(read=True),
                expiry=datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(hours=48)
            )
            
            sas_url = f"{blob_client.url}?{sas_token}"
            documents.append({"file_name": file_name, "file_url": sas_url})

        except Exception as e:
            LOGGER.error(f"Failed to process blob {blob.name} and generate SAS URL: {e}")
    
    if not documents:
        return pd.DataFrame()
        
    return pd.DataFrame(documents).drop_duplicates().reset_index(drop=True)

def load_all_ground_truth_json():
    """Loads all ground truth JSON files from the 'Ground Truth' virtual folder in Azure."""
    if not container_client: return {}
    LOGGER.info("Loading all ground truth JSON data from Azure Blob Storage.")
    gt_data_dict = {}
    
    blob_list = container_client.list_blobs(name_starts_with=f"{PATHS['ground_truth']}/")

    for blob in blob_list:
        if blob.name.endswith(".json"):
            try:
                blob_client = container_client.get_blob_client(blob.name)
                downloader = blob_client.download_blob()
                data = json.loads(downloader.readall())
                
                file_name = data.get("document_id")
                if file_name:
                    gt_data_dict[file_name] = data
                else:
                    LOGGER.warning(f"Skipping blob {blob.name}: missing 'document_id' key.")
            except Exception as e:
                LOGGER.error(f"Failed to load or parse blob {blob.name}: {e}")
    
    return gt_data_dict

def get_raw_document_bytes(file_name: str) -> bytes | None:
    """Downloads the content of a raw document from Azure Blob Storage into memory."""
    if not container_client: return None
    try:
        blob_name = f"{PATHS['raw']}/{file_name}"
        blob_client = container_client.get_blob_client(blob_name)
        if blob_client.exists():
            return blob_client.download_blob().readall()
        else:
            LOGGER.warning(f"Raw document blob not found: {blob_name}")
            return None
    except Exception as e:
        LOGGER.error(f"Failed to download raw document blob {file_name}: {e}")
        return None

def get_llm_extraction_json(file_name: str, run_name: str) -> dict | None:
    """
    Downloads and parses an LLM extraction JSON file from a specific run folder 
    in Azure Blob Storage.
    """
    if not container_client: return None
    try:
        json_filename = _get_clean_filename(file_name, ".json")
        blob_name = f"{PATHS['extracted']}/{run_name}/{json_filename}"
        blob_client = container_client.get_blob_client(blob_name)
        if blob_client.exists():
            downloader = blob_client.download_blob()
            return json.loads(downloader.readall())
        else:
            LOGGER.warning(f"LLM extraction blob not found: {blob_name}")
            return None
    except Exception as e:
        LOGGER.error(f"Failed to download or parse LLM extraction blob {file_name} from run {run_name}: {e}")
        return None