import warnings
from typing import Dict, List, Any, Set, Tuple, Optional
from rapidfuzz import fuzz

warnings.filterwarnings("ignore")

class FormatBoundingBoxes():
    """
    Process the OCR result to find and assign bounding boxes to specific entities extracted from a document.

    The class is initialized with the JSON from the OCR result and pre-processes it for efficient access.
    The main method 'run' enriches a specific extraction structure with bounding box and confidence information.
    """
    _HORIZONTAL_THRESHOLD_FACTOR = 4.0
    _VERTICAL_THRESHOLD_FACTOR = 2.5
    _SIMILARITY_THRESHOLD = 90

    # Keys that require duplicate checks
    _DUPLICATE_SENSITIVE_KEYS = {"transaction", "holding"}

    def __init__(self, json_text: dict[str, Any]):
        """
        Initializes the formatter with the OCR result.

        Args:
            json_text (Dict[str, Any]): The raw JSON from the DI service.
        """
        self.words_by_page = self._create_word_map(json_text)
        
    def run(self, specific_extraction: dict[str, Any]):
        """
        Enriches the specific extraction with bounding boxes and confidences.

        Args:
            specific_extraction (Dict[str, Any]): The specific extraction to enrich.
            json_text (Dict[str, Any]): The raw JSON from the DI service.

        Returns:
            Dict[str, Any]: The enriched specific extraction.
        """
        for key, items in specific_extraction.items():
            if not isinstance(items, list): continue

            used_boxes: Set[Tuple[int, float, float, float, float]] = set()

            check_for_duplicates = key in self._DUPLICATE_SENSITIVE_KEYS

            for item in items:
                self._enrich_item(item, used_boxes if check_for_duplicates else None)

        return specific_extraction
    
    def _are_words_similar(self, word1: str, word2: str) -> bool:
        """
        Compares two words using a fuzzy matching ratio.
        Cleans trailing punctuation before comparison.
        """
        clean_word1 = word1.rstrip('.,')
        clean_word2 = word2.rstrip('.,')
        return fuzz.ratio(clean_word1, clean_word2) >= self._SIMILARITY_THRESHOLD

    def _create_word_map(self, json_text: dict[str, Any]) -> dict[int, list[dict]]:
        """
        Creates a dictionary of words by page from the json text.
        Args:
            json_text (dict): The json text from the DI service.
        Returns:
            dict: A dictionary of words by page.
        """
        words_by_page = {}
        for page in json_text["pages"]:
            page_number = page["pageNumber"]
            page_words = []
            for word in page["words"]:
                if "polygon" not in word:
                     page_words.append({
                        "content": word["content"],
                        "confidence": word["confidence"],
                        "x0": 0, "y0": 0, "x1": 0, "y1": 0
                    })
                     
                else:
                    polygon = word["polygon"]
                    x0 = min([polygon[0], polygon[2], polygon[4], polygon[6]]) # Left border
                    y0 = min([polygon[1], polygon[3], polygon[5], polygon[7]]) # Top border
                    x1 = max([polygon[0], polygon[2], polygon[4], polygon[6]]) # Right border
                    y1 = max([polygon[1], polygon[3], polygon[5], polygon[7]]) # Bottom border
                    
                    page_words.append({
                        "content": word["content"],
                        "confidence": word["confidence"],
                        "x0": x0, "y0": y0, "x1": x1, "y1": y1
                    })

            words_by_page[page_number] = page_words

        return words_by_page
    
    def _enrich_item(self, item: Dict[str, Any], used_boxes: Optional[Set[Tuple]] = None):
        """
        Enriches a single item (transaction or generic) with its bounding box.

        Args:
            item (Dict[str, Any]): The item to enrich.
            used_boxes (Optional[Set[Tuple]]): A set of used bounding boxes.
                If None, no duplicate check is performed.
        """
        data = item.get("data", {})
        for field_data in data.values():

            # Default null values
            field_data['confidence'] = None
            field_data['bounding_regions'] = None

            value_to_find = field_data.get("value")

            page_num_raw = field_data.get("page_number")
            page_num = None

            if isinstance(page_num_raw, list):
                if page_num_raw:
                    page_num = page_num_raw[0]
            elif isinstance(page_num_raw, int):
                page_num = page_num_raw

            if not all([value_to_find, isinstance(value_to_find, str), page_num]):
                continue

            page_words = self.words_by_page.get(page_num)
            if not page_words:
                continue

            all_matches = self._find_all_chained_phrase_occurrences(value_to_find, page_words)

            if not all_matches:
                continue

            # Logic to select the correct match
            if used_boxes is not None:
                # Mode "duplicate": find the first unused match
                for matched_words in all_matches:
                    bbox, confidence = self._calculate_match_details(matched_words)
                    bbox_tuple = (page_num, bbox['x0'], bbox['y0'], bbox['x1'], bbox['y1'])

                    if bbox_tuple not in used_boxes:
                        self._apply_enrichment(field_data, bbox, confidence)
                        used_boxes.add(bbox_tuple)
                        break # Match found and assigned, move to the next field
            else:
                # Mode "generic": use the first match found
                matched_words = all_matches[0]
                bbox, confidence = self._calculate_match_details(matched_words)
                self._apply_enrichment(field_data, bbox, confidence)
    
    def _calculate_match_details(self, matched_words: List[Dict]) -> Tuple[Dict, float]:
        """
        Calculates the bounding box and average confidence for a list of matched words.

        Args:
            matched_words (List[Dict]): The list of matched words.

        Returns:
            Tuple[Dict, float]: A tuple containing the bounding box and the average confidence.
        """
        if not matched_words:
            return {}, 0.0
            
        bbox = {
            "x0": min(w['x0'] for w in matched_words),
            "y0": min(w['y0'] for w in matched_words),
            "x1": max(w['x1'] for w in matched_words),
            "y1": max(w['y1'] for w in matched_words)
        }
        
        confidence = round(sum(w['confidence'] for w in matched_words) / len(matched_words), 4)
        return bbox, confidence
    
    def _apply_enrichment(self, field_data: Dict, bbox: Dict, confidence: float):
        """
        Applies the bounding box and confidence to the field data.
        """
        field_data['confidence'] = confidence
        field_data['bounding_regions'] = [
            {"x": bbox['x0'], "y": bbox['y0']},
            {"x": bbox['x1'], "y": bbox['y0']},
            {"x": bbox['x1'], "y": bbox['y1']},
            {"x": bbox['x0'], "y": bbox['y1']}
        ]
    
    def _are_words_close(self, word1 : dict[str, Any], word2: dict[str, Any]) -> bool:
        """
        Checks if two words are close to each other, either horizontally or vertically.
        Args:
            word1 (dict): The first word.
            word2 (dict): The second word.
        Returns:
            bool: True if the words are close, False otherwise.
        """
        height1 = word1['y1'] - word1['y0']
        
        horizontal_threshold = height1 * self._HORIZONTAL_THRESHOLD_FACTOR
        vertical_threshold = height1 * self._VERTICAL_THRESHOLD_FACTOR

        # Check if the words are close horizontally
        y_overlap = (word1['y0'] < word2['y1'] and word1['y1'] > word2['y0'])
        # Calculate the absolute horizontal gap
        horizontal_gap = abs(word2['x0'] - word1['x1']) if word2['x0'] > word1['x1'] else abs(word1['x0'] - word2['x1'])
        # Check if the horizontal gap is within the threshold
        is_horizontally_close = y_overlap and (horizontal_gap < horizontal_threshold)

        # Check if the words are close vertically
        x_overlap = (word1['x0'] < word2['x1'] and word1['x1'] > word2['x0'])
        # Calculate the absolute vertical gap
        vertical_gap = abs(word2['y0'] - word1['y1']) if word2['y0'] > word1['y1'] else abs(word1['y0'] - word2['y1'])
        # Check if the vertical gap is within the threshold
        is_vertically_close = x_overlap and (vertical_gap < vertical_threshold)

        return is_horizontally_close or is_vertically_close
    
    def _find_all_chained_phrase_occurrences(self, phrase : str, page_words : list[dict]) -> list[list[dict]]:
        """
        Finds all the occurrences of a phrase, even if they are multiline or have OCR noise.
        args:
            phrase: the phrase to find
            page_words: the words on the page
        returns:
            all_found_chains: a list of all the found chains
        """
        target_words = phrase.replace('|', ' ').split()
        if not target_words: return []
        
        all_found_chains = []

        # 1. Find the first word of the phrase
        for i in range(len(page_words)):
            if self._are_words_similar(page_words[i]['content'], target_words[0]):
                # Initialize the chain with the first word
                chain = [page_words[i]]
                last_word_index = i
                # 2. Find the rest of the words in the chain
                for j in range(1, len(target_words)):
                    next_word_found = False
                    # Looks for the next word in the page
                    for k in range(last_word_index + 1, len(page_words)):
                        if self._are_words_similar(page_words[k]['content'], target_words[j]):
                            # Checks if any word in the chain is close to the next word
                            if any(self._are_words_close(existing_word, page_words[k]) for existing_word in chain):
                                chain.append(page_words[k])
                                last_word_index = k
                                next_word_found = True
                                break
                    # If a word in the chain is not found, this chain is invalid
                    if not next_word_found: break
                # If the chain is as long as the target phrase, it's a valid match
                if len(chain) == len(target_words):
                    all_found_chains.append(chain)
        return all_found_chains