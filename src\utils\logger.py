import logging
import sys

def get_logger(name, level=logging.INFO):
    """
    Get a logger instance with the specified name and level.
    """
    logger = logging.getLogger(name)
    logger.setLevel(level)
    logger.propagate = False

    # If the logger already has handlers, clear them.
    if logger.hasHandlers():
        logger.handlers.clear()

    # Add our handler, ensuring it's the only one.
    console_handler = logging.StreamHandler(sys.stdout)
    formatter = logging.Formatter('%(asctime)s | %(name)s | %(levelname)s | %(message)s')
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

    return logger