{"cells": [{"cell_type": "code", "execution_count": 1, "id": "48decc4c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\"choices\":[{\"content_filter_results\":{\"hate\":{\"filtered\":false,\"severity\":\"safe\"},\"self_harm\":{\"filtered\":false,\"severity\":\"safe\"},\"sexual\":{\"filtered\":false,\"severity\":\"safe\"},\"violence\":{\"filtered\":false,\"severity\":\"safe\"}},\"finish_reason\":\"stop\",\"index\":0,\"logprobs\":null,\"message\":{\"annotations\":[],\"content\":\"The capital of France is **Paris**.\",\"refusal\":null,\"role\":\"assistant\"}}],\"created\":1758047865,\"id\":\"chatcmpl-CGUqfM9cVnndBmQNX1k6xUsau9rK4\",\"model\":\"gpt-4o-2024-11-20\",\"object\":\"chat.completion\",\"prompt_filter_results\":[{\"prompt_index\":0,\"content_filter_results\":{\"hate\":{\"filtered\":false,\"severity\":\"safe\"},\"self_harm\":{\"filtered\":false,\"severity\":\"safe\"},\"sexual\":{\"filtered\":false,\"severity\":\"safe\"},\"violence\":{\"filtered\":false,\"severity\":\"safe\"}}}],\"system_fingerprint\":\"fp_ee1d74bde0\",\"usage\":{\"completion_tokens\":10,\"completion_tokens_details\":{\"accepted_prediction_tokens\":0,\"audio_tokens\":0,\"reasoning_tokens\":0,\"rejected_prediction_tokens\":0},\"prompt_tokens\":25,\"prompt_tokens_details\":{\"audio_tokens\":0,\"cached_tokens\":0},\"total_tokens\":35}}\n", "\n"]}], "source": ["import requests\n", "import json\n", "\n", "url = \"*************************************/adminconsole/openai/deployments/gpt-4o/chat/completions?api-version=2025-01-01-preview\"\n", "\n", "payload = json.dumps({\n", "  \"messages\": [\n", "    {\n", "      \"role\": \"system\",\n", "      \"content\": \"You are a helpful AI assistant.\"\n", "    },\n", "    {\n", "      \"role\": \"user\",\n", "      \"content\": \"What is the capital of France?\"\n", "    }\n", "  ],\n", "  \"max_tokens\": 150,\n", "  \"temperature\": 0.7,\n", "  \"frequency_penalty\": 0,\n", "  \"presence_penalty\": 0,\n", "  \"top_p\": 0.95,\n", "  \"stop\": None,\n", "  \"stream\": False\n", "})\n", "headers = {\n", "  'api-key': 'f584a33012fd467283f2fb4bab97dc93',\n", "  'Content-Type': 'application/json'\n", "}\n", "\n", "response = requests.request(\"POST\", url, headers=headers, data=payload)\n", "\n", "print(response.text)"]}, {"cell_type": "code", "execution_count": null, "id": "4725a36d", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 5}