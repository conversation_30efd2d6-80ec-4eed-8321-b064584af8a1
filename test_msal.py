from msal import PublicClientApplication, ConfidentialClientApplication

# app = ConfidentialClientApplication(
#     client_id="9c463444-4962-45d0-9413-a4446aa2faf6",
#     authority="https://login.microsoftonline.com/5b973f99-77df-4beb-b27d-aa0c70b8482c"
#     )

# # initialize result variable to hole the token response
# result = None 

# # We now check the cache to see
# # whether we already have some accounts that the end user already used to sign in before.
# accounts = app.get_accounts()
# print(accounts)
# if accounts:
#     # If so, you could then somehow display these accounts and let end user choose
#     print("Pick the account you want to use to proceed:")
#     for a in accounts:
#         print(a["username"])
#     # Assuming the end user chose this one
#     chosen = accounts[0]
#     # Now let's try to find a token in cache for this account
#     result = app.acquire_token_silent(["User.Read"], account=chosen)

# if not result:
#     # So no suitable token exists in cache. Let's get a new one from Azure AD.
#     result = app.acquire_token_for_client(scopes=["User.Read"])
# if "access_token" in result:
#     print(result["access_token"])  # Yay!
# else:
#     print(result.get("error"))
#     print(result.get("error_description"))
#     print(result.get("correlation_id"))  # You may need this when reporting a bug
    
"""
The configuration file would look like this:

{
    "authority": "https://login.microsoftonline.com/common",
    "client_id": "your_client_id",
    "scope": ["User.ReadBasic.All"],
        // You can find the other permission names from this document
        // https://docs.microsoft.com/en-us/graph/permissions-reference
    "endpoint": "https://graph.microsoft.com/v1.0/users"
        // You can find more Microsoft Graph API endpoints from Graph Explorer
        // https://developer.microsoft.com/en-us/graph/graph-explorer
}

You can then run this sample with a JSON configuration file:

    python sample.py parameters.json
"""

import sys  # For simplicity, we'll read config file from 1st CLI param sys.argv[1]
import json
import logging

import requests
import msal


# Optional logging
# logging.basicConfig(level=logging.DEBUG)  # Enable DEBUG log for entire script
# logging.getLogger("msal").setLevel(logging.INFO)  # Optionally disable MSAL DEBUG logs

config = {
    "client_id": "9c463444-4962-45d0-9413-a4446aa2faf6",
    "client_secret": "****************************************",
    "authority": "https://login.microsoftonline.com/5b973f99-77df-4beb-b27d-aa0c70b8482c",
    "scope": ["openid profile email"]
}

# Create a preferably long-lived app instance which maintains a token cache.
app = msal.PublicClientApplication(
    config["client_id"], 
    authority=config["authority"], 
    # client_credential=config["client_secret"]

    # token_cache=...  # Default cache is in memory only.
                       # You can learn how to use SerializableTokenCache from
                       # https://msal-python.readthedocs.io/en/latest/#msal.SerializableTokenCache
    )

# The pattern to acquire a token looks like this.
result = None

accounts = app.get_accounts(username=config.get("username"))
print(accounts)
if accounts:
    logging.info("Account(s) exists in cache, probably with token too. Let's try.")
    print("Account(s) already signed in:")
    for a in accounts:
        print(a["username"])
    chosen = accounts[0]  # Assuming the end user chose this one to proceed
    print("Proceed with account: %s" % chosen["username"])
    # Now let's try to find a token in cache for this account
    result = app.acquire_token_silent(config["scope"], account=chosen)

if not result:
    logging.info("No suitable token exists in cache. Let's get a new one from AAD.")
    print("A local browser window will be open for you to sign in. CTRL+C to cancel.")
    result = app.acquire_token_interactive(  # Only works if your app is registered with redirect_uri as http://localhost
        config["scope"],
        #parent_window_handle=...,  # If broker is enabled, you will be guided to provide a window handle
        #login_hint=config.get("username"),  # Optional.
            # If you know the username ahead of time, this parameter can pre-fill
            # the username (or email address) field of the sign-in page for the user,
            # Often, apps use this parameter during reauthentication,
            # after already extracting the username from an earlier sign-in
            # by using the preferred_username claim from returned id_token_claims.

        #prompt=msal.Prompt.SELECT_ACCOUNT,  # Or simply "select_account". Optional. It forces to show account selector page
        #prompt=msal.Prompt.CREATE,  # Or simply "create". Optional. It brings user to a self-service sign-up flow.
            # Prerequisite: https://docs.microsoft.com/en-us/azure/active-directory/external-identities/self-service-sign-up-user-flow
        )
    print(f"RESULT: {result}")

if "access_token" in result:
    # Calling graph using the access token
    graph_response = requests.get(  # Use token to call downstream service
        config["endpoint"],
        headers={'Authorization': 'Bearer ' + result['access_token']},)
    print("Graph API call result: %s ..." % graph_response.text[:100])
else:
    print(result.get("error"))
    print(result.get("error_description"))
    print(result.get("correlation_id"))  # You may need this when reporting a bug