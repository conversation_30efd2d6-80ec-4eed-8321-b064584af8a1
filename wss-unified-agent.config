###############################################################
# WhiteSource Unified-Agent configuration file
###############################################################
# GENERAL SCAN MODE: Files and Package Managers
###############################################################
# Organization vitals
######################

apiKey= 
#userKey is required if WhiteSource administrator has enabled "Enforce user level access" option
userKey=
#requesterEmail=<EMAIL>

projectName=Independence Document Processing
projectVersion=
projectToken=
#projectTag= key:value

productName=Independence Document Processing
productVersion=
productToken=

#projectPerFolder=true
#projectPerFolderIncludes=
#projectPerFolderExcludes=

#wss.connectionTimeoutMinutes=60

# Change the below URL to your WhiteSource server.
# Use the 'WhiteSource Server URL' which can be retrieved
# from your 'Profile' page on the 'Server URLs' panel.
# Then, add the '/agent' path to it.
wss.url=https://vmey.whitesourcesoftware.com/agent
#wss.url=https://app.whitesourcesoftware.com/agent
#wss.url=https://app-eu.whitesourcesoftware.com/agent
#wss.url=https://saas-eu.whitesourcesoftware.com/agent

############
# Policies #
############
checkPolicies=false
forceCheckAllDependencies=false
forceUpdate=false
forceUpdate.failBuildOnPolicyViolation=false
#updateInventory=false

###########
# General #
###########
#offline=true
#updateType=APPEND
#scanComment=
#failErrorLevel=ALL
#requireKnownSha1=false
fileSystemScan=false
#showProgressBar=false
#commandTimeout=900

#generateProjectDetailsJson=true
#generateScanReport=true
#scanReportTimeoutMinutes=10
#scanReportFilenameFormat=

#analyzeFrameworks=true
#analyzeFrameworksReference=

#updateEmptyProject=false

log.level=DEBUG
#log.files.maxFileSize=
#log.files.maxFilesCount=
#log.files.path=

########################################
# Package Manager Dependency resolvers #
########################################
resolveAllDependencies=false
#excludeDependenciesFromNodes=.*commons-io.*,.*maven-model

#npm.resolveDependencies=false
#npm.ignoreSourceFiles=false
#npm.includeDevDependencies=true
#npm.runPreStep=true
#npm.ignoreNpmLsErrors=true
#npm.ignoreScripts=true
#npm.yarnProject=true
#npm.accessToken=
#npm.identifyByNameAndVersion=true
#npm.yarn.frozenLockfile=true
#npm.resolveMainPackageJsonOnly=true
#npm.removeDuplicateDependencies=false
#npm.resolveAdditionalDependencies=true
#npm.failOnNpmLsErrors = 
#npm.projectNameFromDependencyFile = true
#npm.resolveGlobalPackages=true							   
#npm.resolveLockFile=false

#bower.resolveDependencies=false
#bower.ignoreSourceFiles=true
#bower.runPreStep=true

#nuget.resolveDependencies=true
#nuget.runPreStep=true
#nuget.resolvePackagesConfigFiles=false
#nuget.resolveCsProjFiles=true
#nuget.resolveNuspecFiles=false
#nuget.resolveAssetsFiles=true
#nuget.preferredEnvironment=nuget
#nuget.packagesDirectory=
#nuget.ignoreSourceFiles=false

python.resolveDependencies=true
python.ignoreSourceFiles=false
python.ignorePipInstallErrors=false
python.installVirtualenv=true
python.resolveHierarchyTree=true
#python.requirementsFileIncludes=requirements.txt
#python.resolveSetupPyFiles=true
#python.runPipenvPreStep=true
#python.pipenvDevDependencies=true
#python.IgnorePipenvInstallErrors=true
#python.resolveGlobalPackages=true	
#python.localPackagePathsToInstall=/path/to/local/dependency.egg, /path/to/local/dependency.zip	
python.resolvePipEditablePackages=true
#python.path=/path/to/python
#python.pipPath=/path/to/pip
#python.runPoetryPreStep=true
#python.includePoetryDevDependencies=true					  

#maven.ignoredScopes=test provided
#maven.resolveDependencies=false
#maven.ignoreSourceFiles=true
#maven.aggregateModules=true
#maven.ignorePomModules=false
#maven.runPreStep=true
#maven.ignoreMvnTreeErrors=true
#maven.environmentPath=
#maven.m2RepositoryPath=
#maven.downloadMissingDependencies=false
#maven.additionalArguments=
#maven.projectNameFromDependencyFile=true										 

#gradle.resolveDependencies=false
#gradle.ignoreSourceFiles=true
#gradle.aggregateModules=true
#gradle.preferredEnvironment=wrapper
#gradle.wrapperPath=
#gradle.additionalArguments=
#gradle.excludeModules=
#gradle.includeModules=
#gradle.includedConfigurations=
#gradle.ignoredConfigurations=
#gradle.innerModulesAsDependencies=false

#paket.resolveDependencies=false
#paket.ignoredGroups=
#paket.ignoreSourceFiles=false
#paket.runPreStep=true
#paket.exePath=

#go.resolveDependencies=false
#go.collectDependenciesAtRuntime=true
#go.dependencyManager=
#go.ignoreSourceFiles=true
#go.glide.ignoreTestPackages=false
#go.gogradle.enableTaskAlias=true

#go.modules.resolveDependencies=true
#go.modules.ignoreSourceFiles=false
#go.modules.removeDuplicateDependencies=false
#go.modules.includeTestDependecies=true

#ruby.resolveDependencies=false
#ruby.ignoreSourceFiles=false
#ruby.installMissingGems=true
#ruby.runBundleInstall=true
#ruby.overwriteGemFile=true

#sbt.resolveDependencies=false
#sbt.ignoreSourceFiles=true
#sbt.aggregateModules=true
#sbt.runPreStep=true
#sbt.includedScopes=

#php.resolveDependencies=false
#php.runPreStep=true
#php.includeDevDependencies=true
#php.removeDuplicateDependencies=false
#php.ignoreSourceFiles=false

#html.resolveDependencies=false

#cocoapods.resolveDependencies=false
#cocoapods.runPreStep=true
#cocoapods.ignoreSourceFiles=false

#hex.resolveDependencies=false
#hex.runPreStep=true
#hex.ignoreSourceFiles=false
#hex.aggregateModules=true

#ant.resolveDependencies=false
#ant.pathIdIncludes=.*
#ant.external.parameters=

#r.resolveDependencies=false
#r.runPreStep=true
#r.ignoreSourceFiles=false
#r.cranMirrorUrl=
#r.packageManager=None

#cargo.resolveDependencies=false
#cargo.runPreStep=true
#cargo.ignoreSourceFiles=false

#haskell.resolveDependencies=false
#haskell.runPreStep=true
#haskell.ignoreSourceFiles=false
#haskell.ignorePreStepErrors=true

#ocaml.resolveDependencies=false
#ocaml.runPrepStep=true
#ocaml.ignoreSourceFiles=false
#ocaml.switchName=
#ocaml.ignoredScopes=none
#ocaml.aggregateModules=true

#bazel.resolveDependencies=false
#bazel.runPrepStep=true

#conda.resolveDependencies=false

###########################################################################################
# Includes/Excludes Glob patterns - Please use only one exclude line and one include line #
###########################################################################################
#includes=**/*.c **/*.cc **/*.cp **/*.cpp **/*.cxx **/*.c++ **/*.h **/*.hpp **/*.hxx
#includes=**/*.m **/*.mm  **/*.js **/*.php
#includes=**/*.jar
#includes=**/*.gem **/*.rb
#includes=**/*.dll **/*.cs **/*.nupkg
#includes=**/*.tgz **/*.gzip **/*.tar.bz2
#includes=**/*.zip **/*.tar.gz **/*.egg **/*.whl **/*.py

#Exclude file extensions or specific directories by adding **/*.<extension> or **/<excluded_dir>/**
#excludes=**/.* **/node_modules **/src/test **/testdata **/*sources.jar **/*javadoc.jar

case.sensitive.glob=false
followSymbolicLinks=true

######################
# Archive properties #
######################
#archiveExtractionDepth=2
#archiveIncludes=**/*.war **/*.ear
#archiveExcludes=**/*sources.jar

##############
# SCAN MODES #
##############

# Docker images
################
#docker.scanImages=true
#docker.includes=.*.*
#docker.excludes=
#docker.pull.enable=true
#docker.pull.images=.*.*
#docker.pull.maxImages=10
#docker.pull.tags=.*.*
#docker.pull.digest=
#docker.delete.force=true
#docker.login.sudo=false
#docker.projectNameFormat={repositoryNameAndTag|repositoryName|default}
#docker.scanTarFiles=true

#docker.aws.enable=true
#docker.aws.registryIds=

#docker.azure.enable=true
#docker.azure.userName=
#docker.azure.userPassword=
#docker.azure.registryNames=
#docker.azure.authenticationType=containerRegistry
#docker.azure.registryAuthenticationParameters=<registry1UserName>:<registry1Password> <registry2UserName>:<registry2Password>

#docker.gcr.enable=true
#docker.gcr.account=
#docker.gcr.repositories=

#docker.artifactory.enable=true
#docker.artifactory.url=
#docker.artifactory.pullUrl=
#docker.artifactory.userName=
#docker.artifactory.userPassword=
#docker.artifactory.repositoriesNames=
#docker.artifactory.dockerAccessMethod=

#docker.hub.enabled=true
#docker.hub.userName=
#docker.hub.userPassword=
#docker.hub.organizationsNames=

# Docker containers
####################
#docker.scanContainers=true
#docker.containerIncludes=.*.*
#docker.containerExcludes=

# Linux package manager settings
################################
#scanPackageManager=true

# Serverless settings
######################
#serverless.provider=
#serverless.scanFunctions=true
#serverless.includes=
#serverless.excludes=
#serverless.region=
#serverless.maxFunctions=10

# Artifactory settings
########################
#artifactory.enableScan=true
#artifactory.url=
#artifactory.accessToken=
#artifactory.repoKeys=
#artifactory.userName=
#artifactory.userPassword=

##################
# Proxy settings #
##################
#proxy.host=
#proxy.port=
#proxy.user=
#proxy.pass=

################
# SCM settings #
################
#scm.type=
#scm.user=
#scm.pass=
#scm.ppk=
#scm.url=
#scm.branch=
#scm.tag=
#scm.npmInstall=
#scm.npmInstallTimeoutMinutes=
#scm.repositoriesFile=